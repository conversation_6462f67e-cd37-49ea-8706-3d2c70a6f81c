/**
 * PumpFun API Client using Supabase MCP for AP3X-pump-tokens project
 * Tables: new_tokens, bonding_tokens, graduated_tokens
 */

export interface PumpFunTokenResponse {
  // Core token data from the JSONB data field
  tokenAddress?: string;
  mint?: string;
  name?: string;
  symbol?: string;
  description?: string;
  image_uri?: string;
  metadata_uri?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  show_name?: boolean;
  created_timestamp?: number;
  nsfw?: boolean;
  market_cap?: number;
  reply_count?: number;
  last_reply?: number;
  complete?: boolean;
  total_supply?: number;
  creator?: string;
  bonding_curve?: string;
  associated_bonding_curve?: string;
  usd_market_cap?: number;
  virtual_sol_reserves?: number;
  virtual_token_reserves?: number;
  real_sol_reserves?: number;
  real_token_reserves?: number;
  last_trade_timestamp?: number;
  king_of_the_hill_timestamp?: number | null;
  market_id?: string | null;
  inverted?: boolean | null;
  is_currently_live?: boolean;
  raydium_pool?: string | null;

  // Enriched data fields
  fdv?: number;
  logo?: string;
  dexId?: string;
  chainId?: string;
  decimals?: string;
  priceUsd?: string;
  liquidity?: string;
  marketCap?: number;
  volume_h1?: number;
  volume_h6?: number;
  volume_h24?: number;
  pairAddress?: string;
  priceNative?: string;
  txns_h1_buys?: number;
  txns_h1_sells?: number;
  txns_h24_buys?: number;
  txns_h24_sells?: number;
  activity_level?: string;
  is_trending_up?: boolean;
  priceChange_h1?: number;
  priceChange_h6?: number;
  priceChange_h24?: number;
  total_buys_24h?: number;
  total_sells_24h?: number;
  total_txns_24h?: number;
  buy_sell_ratio_24h?: string;
  bondingCurveProgress?: number;
  graduatedAt?: string;

  // Prefixed pumpfun fields
  pumpfun_nsfw?: boolean;
  pumpfun_creator?: string;
  pumpfun_twitter?: string;
  pumpfun_website?: string;
  pumpfun_telegram?: string;
  pumpfun_complete?: boolean;
  pumpfun_description?: string;
  pumpfun_metadata_uri?: string;
  pumpfun_raydium_pool?: string | null;
  pumpfun_bonding_curve?: string;
  pumpfun_created_timestamp?: number;
  pumpfun_is_currently_live?: boolean;
  pumpfun_virtual_sol_reserves?: number;
  pumpfun_virtual_token_reserves?: number;
  pumpfun_associated_bonding_curve?: string;

  // Additional fields
  [key: string]: any;
}

export type PumpFunCategory = 'new' | 'bonding' | 'graduated';

export interface PumpFunAllCategoriesResponse {
  'new': PumpFunTokenResponse[];
  'bonding': PumpFunTokenResponse[];
  'graduated': PumpFunTokenResponse[];
}

import { supabaseIntegration, type SupabaseRow } from './supabaseIntegration';

export class PumpFunAPI {
  constructor() {}

  /**
   * Get recent tokens from a specific category
   */
  async getRecentTokensByCategory(category: PumpFunCategory, limit: number = 50): Promise<PumpFunTokenResponse[]> {
    try {
      let supabaseRows: SupabaseRow[] = [];

      // Use the Supabase integration to get data
      switch (category) {
        case 'new':
          supabaseRows = await supabaseIntegration.getNewTokens(limit);
          break;
        case 'bonding':
          supabaseRows = await supabaseIntegration.getBondingTokens(limit);
          break;
        case 'graduated':
          supabaseRows = await supabaseIntegration.getGraduatedTokens(limit);
          break;
        default:
          throw new Error(`Unknown category: ${category}`);
      }

      // Transform the Supabase response to our expected format
      return supabaseRows.map(row => this.transformSupabaseRow(row));
    } catch (error) {
      console.error(`Error fetching ${category} tokens:`, error);
      throw error;
    }
  }

  /**
   * Get recent tokens from all categories at once
   */
  async getRecentTokensAllCategories(limit: number = 50): Promise<PumpFunAllCategoriesResponse> {
    try {
      const [newTokens, bondingTokens, graduatedTokens] = await Promise.all([
        this.getRecentTokensByCategory('new', limit),
        this.getRecentTokensByCategory('bonding', limit),
        this.getRecentTokensByCategory('graduated', limit)
      ]);

      return {
        'new': newTokens,
        'bonding': bondingTokens,
        'graduated': graduatedTokens
      };
    } catch (error) {
      console.error('Error fetching all categories:', error);
      throw error;
    }
  }

  /**
   * Transform Supabase row to PumpFunTokenResponse format
   */
  private transformSupabaseRow(row: any): PumpFunTokenResponse {
    const data = row.data || {};

    // Extract the main token address (prefer tokenAddress, fallback to mint)
    const tokenAddress = data.tokenAddress || data.mint || data._pumpfun_direct_original?.mint || row.token_address;

    return {
      // Core fields
      tokenAddress,
      mint: tokenAddress,
      name: data.name || data._pumpfun_direct_original?.name || '',
      symbol: data.symbol || data._pumpfun_direct_original?.symbol || '',
      description: data.pumpfun_description || data._pumpfun_direct_original?.description || '',
      image_uri: data._pumpfun_direct_original?.image_uri || data.logo || '',
      metadata_uri: data.pumpfun_metadata_uri || data._pumpfun_direct_original?.metadata_uri || '',
      twitter: data.pumpfun_twitter || data._pumpfun_direct_original?.twitter || '',
      telegram: data.pumpfun_telegram || data._pumpfun_direct_original?.telegram || '',
      website: data.pumpfun_website || data._pumpfun_direct_original?.website || '',

      // Market data
      market_cap: data.marketCap || data._pumpfun_direct_original?.market_cap || 0,
      usd_market_cap: data._pumpfun_direct_original?.usd_market_cap || data.marketCap || 0,
      priceUsd: data.priceUsd || '0',
      priceNative: data.priceNative || '0',

      // Trading data
      volume_h24: data.volume_h24 || 0,
      total_buys_24h: data.total_buys_24h || 0,
      total_sells_24h: data.total_sells_24h || 0,
      total_txns_24h: data.total_txns_24h || 0,
      priceChange_h24: data.priceChange_h24 || 0,

      // PumpFun specific
      creator: data.pumpfun_creator || data._pumpfun_direct_original?.creator || '',
      bonding_curve: data.pumpfun_bonding_curve || data._pumpfun_direct_original?.bonding_curve || '',
      associated_bonding_curve: data.pumpfun_associated_bonding_curve || data._pumpfun_direct_original?.associated_bonding_curve || '',
      complete: data.pumpfun_complete || data._pumpfun_direct_original?.complete || false,
      nsfw: data.pumpfun_nsfw || data._pumpfun_direct_original?.nsfw || false,
      created_timestamp: data.pumpfun_created_timestamp || data._pumpfun_direct_original?.created_timestamp || 0,
      is_currently_live: data.pumpfun_is_currently_live || data._pumpfun_direct_original?.is_currently_live || false,
      virtual_sol_reserves: data.pumpfun_virtual_sol_reserves || data._pumpfun_direct_original?.virtual_sol_reserves || 0,
      virtual_token_reserves: data.pumpfun_virtual_token_reserves || data._pumpfun_direct_original?.virtual_token_reserves || 0,
      raydium_pool: data.pumpfun_raydium_pool || data._pumpfun_direct_original?.raydium_pool || null,

      // Bonding curve progress (for bonding tokens)
      bondingCurveProgress: data.bondingCurveProgress || 0,

      // Graduation info (for graduated tokens)
      graduatedAt: data.graduatedAt || '',

      // Pass through all original data
      ...data
    };
  }



  // Test connection to Supabase
  async testConnection(): Promise<boolean> {
    try {
      return await supabaseIntegration.testConnection();
    } catch (e) {
      return false;
    }
  }

  /**
   * Get new tokens (latest launches)
   */
  async getNewTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('new', limit);
  }

  /**
   * Get bonding tokens (tokens still in bonding curve)
   */
  async getBondingTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('bonding', limit);
  }

  /**
   * Get graduated tokens
   */
  async getGraduatedTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('graduated', limit);
  }

  // Debug via Supabase
  async debugCategory(category: PumpFunCategory): Promise<any> {
    try {
      const tokens = await this.getRecentTokensByCategory(category, 5);
      return {
        success: true,
        sample: tokens.slice(0, 2),
        count: tokens.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        sample: []
      };
    }
  }
}

// Export a default instance for convenience
export const pumpFunAPI = new PumpFunAPI();

// Export factory function (kept for compatibility)
export const createPumpFunAPI = () => new PumpFunAPI();
