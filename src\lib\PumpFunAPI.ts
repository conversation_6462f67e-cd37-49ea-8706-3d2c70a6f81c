/**
 * PumpFun API Client for the AP3X-PumP ingestion service
 * Endpoints:
 *  - GET /api/pumpfun/categories?limit=50
 *  - GET /api/pumpfun/category/{name}?limit=50 (for-you, runners, graduated, featured)
 */

export interface PumpFunTokenResponse {
  mint: string;
  name: string;
  symbol: string;
  creator: string;
  description: string;
  curated_description?: string;
  image_url: string;
  video_url?: string;
  banner_url?: string;
  website_url?: string;
  twitter_url?: string;
  telegram_url?: string;
  metadata_url?: string;
  initial_market_cap_usd: number;
  current_market_cap_usd?: number;
  bonding_curve_address?: string;
  associated_bonding_curve?: string;
  is_nsfw: boolean;
  is_banned: boolean;
  is_currently_live: boolean;
  king_of_the_hill_timestamp?: string | null;
  raydium_pool?: string | null;
  created_at: string;
  updated_at: string;
}

export type PumpFunCategory = 'for-you' | 'graduated' | 'runners' | 'featured';

export interface PumpFunAllCategoriesResponse {
  'for-you': PumpFunTokenResponse[];
  graduated: PumpFunTokenResponse[];
  runners: PumpFunTokenResponse[];
  featured: PumpFunTokenResponse[];
}

export class PumpFunAPI {
  // Use ingestion server directly - no more Supabase browser calls
  private baseUrl = (import.meta as any).env?.VITE_INGESTION_API_URL || '';

  constructor() {}

  /**
   * Get recent tokens from a specific category
   */
  async getRecentTokensByCategory(category: PumpFunCategory, limit: number = 50): Promise<PumpFunTokenResponse[]> {
    const base = this.baseUrl || '';
    const url = `${base}/api/pumpfun/category/${encodeURIComponent(category)}?limit=${limit}`;
    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Ingestion API error ${res.status} for ${category}`);
    }
    const data = await res.json();
    return data?.data?.tokens || [];
  }



  /**
   * Get recent tokens from all categories at once
   */
  async getRecentTokensAllCategories(limit: number = 50): Promise<PumpFunAllCategoriesResponse> {
    const base = this.baseUrl || '';
    const url = `${base}/api/pumpfun/categories?limit=${limit}`;
    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Ingestion API error ${res.status} for categories`);
    }
    const data = await res.json();
    const buckets = data?.data || {};
    return {
      'for-you': buckets['for-you'] || [],
      'graduated': buckets['graduated'] || [],
      'runners': buckets['runners'] || [],
      'featured': buckets['featured'] || []
    };
  }

  // Test connection to ingestion API
  async testConnection(): Promise<boolean> {
    try {
      const base = this.baseUrl || '';
      const res = await fetch(`${base}/api/health`);
      return res.ok;
    } catch (e) {
      return false;
    }
  }

  /**
   * Get trending tokens (alias for 'for-you' category)
   */
  async getTrendingTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('for-you', limit);
  }



  /**
   * Get graduated tokens
   */
  async getGraduatedTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('graduated', limit);
  }

  /**
   * Get runner tokens (trending/popular)
   */
  async getRunnerTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('runners', limit);
  }

  // Debug via ingestion routes
  async debugCategory(category: PumpFunCategory): Promise<any> {
    const base = this.baseUrl || '';
    const url = `${base}/api/pumpfun/category/${encodeURIComponent(category)}?limit=5`;
    const res = await fetch(url);
    const data = await res.json().catch(() => ({}));
    return { success: res.ok, sample: data?.data?.tokens?.slice(0, 2) || [] };
  }
}

// Export a default instance for convenience
export const pumpFunAPI = new PumpFunAPI();

// Export factory function (kept for compatibility)
export const createPumpFunAPI = () => new PumpFunAPI();
