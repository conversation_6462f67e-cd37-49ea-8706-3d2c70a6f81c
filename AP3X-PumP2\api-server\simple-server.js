import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';
import dotenv from 'dotenv';

// Import our token service
import TokenService from '../src/tokenService.js';

// Load environment variables
dotenv.config();


// Supabase REST config (writer)
const SUPABASE_URL = process.env.SUPABASE_URL; // e.g. http://137.131.0.172:8082/rest/v1
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Use node-fetch for REST writes (Node 18+ has global fetch but we keep this explicit)
import fetch from 'node-fetch';

const supabaseHeaders = () => ({
  'Content-Type': 'application/json',
  'apikey': SUPABASE_SERVICE_ROLE_KEY || '',
  'Authorization': `<PERSON><PERSON> ${SUPABASE_SERVICE_ROLE_KEY || ''}`,
  'Prefer': 'resolution=merge-duplicates'
});

const chunk = (arr = [], size = 500) => {
  const out = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
};

const mapEnrichedToDb = (t = {}) => ({
  mint: t.tokenAddress || t.mint || t._pumpfun_original?.mint || '',
  name: t.name || t._pumpfun_original?.name || null,
  symbol: t.symbol || t._pumpfun_original?.symbol || null,
  image_uri: t.logo || t.image_uri || t._pumpfun_original?.image_uri || null,
  description: t.description || t.pumpfun_description || t._pumpfun_direct_original?.description || null,
  website: t.website || t.pumpfun_website || t._pumpfun_direct_original?.website || null,
  twitter: t.twitter || t.pumpfun_twitter || t._pumpfun_direct_original?.twitter || null,
  telegram: t.telegram || t.pumpfun_telegram || t._pumpfun_direct_original?.telegram || null,
  metadata_uri: t.metadataUri || t.metadata_uri || null,
  bonding_curve_address: t.bonding_curve_address || t.associated_bonding_curve || null,
  associated_bonding_curve: t.associated_bonding_curve || null,
  creator: t.creator || t._pumpfun_direct_original?.creator || null,
  created_timestamp: t.createdAt ? new Date(t.createdAt).getTime() : (t._pumpfun_direct_original?.created_timestamp || null),
  last_trade_timestamp: t._pumpfun_direct_original?.last_trade_timestamp || null,
  total_supply: t.total_supply || t._pumpfun_direct_original?.total_supply || null,
  raydium_pool: t.raydium_pool || t.poolAddress || t._pumpfun_direct_original?.raydium_pool || null,
  virtual_sol_reserves: t.virtualSolReserves || null,
  virtual_token_reserves: t.virtualTokenReserves || null,
  market_cap: t.marketCap ?? null,
  usd_market_cap: t.usd_market_cap ?? t.marketCap ?? null,
  volume_24h: t.volume_h24 ?? t.volume24h ?? null,
  holder_count: t.holder_count ?? t.total_holders ?? null,
  is_nsfw: Boolean(t.is_nsfw || t.nsfw),
  inverted: Boolean(t.inverted),
  is_currently_live: Boolean(t.is_currently_live),
  is_banned: Boolean(t.is_banned),
  hidden: Boolean(t.hidden),
  sources: Array.isArray(t._data_sources) ? t._data_sources : []
});

async function upsertTokensToSupabase(tokens = []) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) return; // not configured
    const base = SUPABASE_URL.replace(/\/?$/, '/');
    const url = base.includes('/rest/v1') ? `${base}pumpfun_tokens` : `${base}/rest/v1/pumpfun_tokens`;
    const rows = tokens.map(mapEnrichedToDb).filter(r => r.mint);
    for (const part of chunk(rows, 500)) {
      const res = await fetch(`${url}?on_conflict=mint`, {
        method: 'POST',
        headers: supabaseHeaders(),
        body: JSON.stringify(part)
      });
      if (!res.ok) {
        const txt = await res.text().catch(() => '');
        console.warn('Supabase upsert tokens failed', res.status, txt);
      }
    }
  } catch (e) {
    console.warn('Supabase upsert tokens error', e.message || String(e));
  }
}

async function upsertCategoryToSupabase(category, items = []) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) return; // not configured
    const base = SUPABASE_URL.replace(/\/?$/, '/');
    const url = base.includes('/rest/v1') ? `${base}pumpfun_categories` : `${base}/rest/v1/pumpfun_categories`;
    const rows = items.map((mint, i) => ({ category, mint, rank: i + 1, score: 0 }));
    for (const part of chunk(rows, 500)) {
      const res = await fetch(`${url}?on_conflict=category,mint`, {
        method: 'POST',
        headers: supabaseHeaders(),
        body: JSON.stringify(part)
      });
      if (!res.ok) {
        const txt = await res.text().catch(() => '');
        console.warn(`Supabase upsert category ${category} failed`, res.status, txt);
      }
    }
  } catch (e) {
    console.warn('Supabase upsert category error', e.message || String(e));
  }
}

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize TokenService
const tokenService = new TokenService();
import WebSocket from 'ws';

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Rate limiting - more generous for simplified API
const limiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '1 minute'
  }
});

app.use('/api/', limiter);

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'AP3X-PumP Token Intelligence API',
      version: '1.0.0',
      description: 'Simple API with 3 endpoints for PumpFun token analysis with full enrichment',
    },
    servers: [{ url: `http://localhost:${PORT}` }]
  },
  apis: ['./simple-server.js']
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Helper function for API responses
const createResponse = (success, data = null, message = '', startTime = null) => {
  const response = {
    success,
    timestamp: new Date().toISOString()
  };

  if (success) {
    response.data = data;
    if (message) response.message = message;
  } else {
    response.error = data;
    response.message = message;
  }

  if (startTime) {
    response.processingTime = `${Date.now() - startTime}ms`;
  }

  return response;
};

// Error handling middleware
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * @swagger
 * /:
 *   get:
 *     summary: API information
 *     responses:
 *       200:
 *         description: API information and available endpoints
 */
app.get('/', (req, res) => {
  res.json(createResponse(true, {
    name: 'AP3X-PumP Token Intelligence API',
    version: '1.0.0',
    description: 'Simple API with 3 endpoints for comprehensive PumpFun token analysis',
    endpoints: {
      health: '/api/health',
      documentation: '/api-docs',
      tokens: {
        new: '/api/tokens/new - Get 50 new tokens with full triple enrichment (all fields)',
        bonding: '/api/tokens/bonding - Get 50 bonding tokens with full triple enrichment (all fields)',
        graduated: '/api/tokens/graduated - Get 50 graduated tokens with full triple enrichment (all fields)'
      }
    },
    features: [
      'Triple enrichment pipeline (PumpFun Moralis + DexScreener + PumpFun Direct API)',
      'Real-time token intelligence',
      'Activity level classification',
      'Performance analytics',
      'Rate limiting protection'
    ]
  }, 'Welcome to AP3X-PumP Token Intelligence API'));
});

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Health check
 *     responses:
 *       200:
 *         description: Service health status
 */
app.get('/api/health', (req, res) => {
  res.json(createResponse(true, {
    status: 'healthy',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  }, 'Service is healthy'));
});

/**
 * @swagger
 * /api/tokens/new:
 *   get:
 *     summary: Get 50 new tokens with full triple enrichment and all fields
 *     responses:
 *       200:
 *         description: Successfully retrieved 50 new tokens with complete data
 */
app.get('/api/tokens/new', asyncHandler(async (req, res) => {
  const startTime = Date.now();

  console.log(`🚀 API Request: New tokens (automatically fetching 50 with all fields)`);

  // Get fully enriched new tokens (includes PumpFun + DexScreener + Frontend API)
  // Always fetch 50 tokens with complete enrichment
  const tokens = await tokenService.getEnrichedNewTokens(50);

  const dexEnrichedCount = tokens.filter(t => t._enriched).length;
  const pumpfunDirectEnrichedCount = tokens.filter(t => t._pumpfun_direct_enriched).length;
  const tripleEnrichedCount = tokens.filter(t => t._enriched && t._pumpfun_direct_enriched).length;

  const statistics = {
    total: tokens.length,
    dexscreener_enriched: dexEnrichedCount,
    pumpfun_direct_enriched: pumpfunDirectEnrichedCount,
    triple_enriched: tripleEnrichedCount,
    enrichment_rates: {
      dexscreener: `${((dexEnrichedCount / tokens.length) * 100).toFixed(1)}%`,
      pumpfun_direct: `${((pumpfunDirectEnrichedCount / tokens.length) * 100).toFixed(1)}%`,
      triple: `${((tripleEnrichedCount / tokens.length) * 100).toFixed(1)}%`
    },
    activity_distribution: {}
  };

  // Calculate activity distribution
  tokens.forEach(token => {
    const activity = token.activity_level || 'unknown';
    statistics.activity_distribution[activity] = (statistics.activity_distribution[activity] || 0) + 1;
  });

  res.json(createResponse(true, {
    tokens,
    statistics
  }, `Retrieved ${tokens.length} new tokens with full triple enrichment`, startTime));
}));

/**
 * @swagger
 * /api/tokens/bonding:
 *   get:
 *     summary: Get 50 bonding tokens with full triple enrichment and all fields
 *     responses:
 *       200:
 *         description: Successfully retrieved 50 bonding tokens with complete data
 */
app.get('/api/tokens/bonding', asyncHandler(async (req, res) => {
  const startTime = Date.now();

  console.log(`🔗 API Request: Bonding tokens (automatically fetching 50 with all fields)`);

  // Get fully enriched bonding tokens (includes PumpFun + DexScreener + Frontend API)
  // Always fetch 50 tokens with complete enrichment
  const tokens = await tokenService.getEnrichedBondingTokens(50);

  const dexEnrichedCount = tokens.filter(t => t._enriched).length;
  const pumpfunDirectEnrichedCount = tokens.filter(t => t._pumpfun_direct_enriched).length;
  const tripleEnrichedCount = tokens.filter(t => t._enriched && t._pumpfun_direct_enriched).length;

  // Calculate bonding curve statistics
  const bondingTokens = tokens.filter(t => t.bondingCurveProgress !== undefined);
  const avgProgress = bondingTokens.length > 0 ?
    bondingTokens.reduce((sum, t) => sum + (parseFloat(t.bondingCurveProgress) || 0), 0) / bondingTokens.length : 0;
  const nearGraduation = bondingTokens.filter(t => parseFloat(t.bondingCurveProgress || 0) > 90).length;

  const statistics = {
    total: tokens.length,
    dexscreener_enriched: dexEnrichedCount,
    pumpfun_direct_enriched: pumpfunDirectEnrichedCount,
    triple_enriched: tripleEnrichedCount,
    enrichment_rates: {
      dexscreener: `${((dexEnrichedCount / tokens.length) * 100).toFixed(1)}%`,
      pumpfun_direct: `${((pumpfunDirectEnrichedCount / tokens.length) * 100).toFixed(1)}%`,
      triple: `${((tripleEnrichedCount / tokens.length) * 100).toFixed(1)}%`
    },
    bonding_curve: {
      tokens_with_progress: bondingTokens.length,
      average_progress: `${avgProgress.toFixed(1)}%`,
      near_graduation: nearGraduation
    },
    activity_distribution: {}
  };

  // Calculate activity distribution
  tokens.forEach(token => {
    const activity = token.activity_level || 'unknown';
    statistics.activity_distribution[activity] = (statistics.activity_distribution[activity] || 0) + 1;
  });

  res.json(createResponse(true, {
    tokens,
    statistics
  }, `Retrieved ${tokens.length} bonding tokens with full triple enrichment`, startTime));
}));

/**
 * @swagger
 * /api/tokens/graduated:
 *   get:
 *     summary: Get 50 graduated tokens with full triple enrichment and all fields
 *     responses:
 *       200:
 *         description: Successfully retrieved 50 graduated tokens with complete data
 */
app.get('/api/tokens/graduated', asyncHandler(async (req, res) => {
  const startTime = Date.now();

  console.log(`🎓 API Request: Graduated tokens (automatically fetching 50 with all fields)`);

  // Get fully enriched graduated tokens (includes PumpFun + DexScreener + Frontend API)
  // Always fetch 50 tokens with complete enrichment
  const tokens = await tokenService.getEnrichedGraduatedTokens(50);

  const dexEnrichedCount = tokens.filter(t => t._enriched).length;
  const pumpfunDirectEnrichedCount = tokens.filter(t => t._pumpfun_direct_enriched).length;
  const tripleEnrichedCount = tokens.filter(t => t._enriched && t._pumpfun_direct_enriched).length;

  // Calculate graduation timing statistics
  const now = new Date();
  const graduationTimes = tokens
    .filter(t => t.graduatedAt)
    .map(t => (now - new Date(t.graduatedAt)) / (1000 * 60 * 60)); // hours ago

  const avgHoursAgo = graduationTimes.length > 0 ?
    graduationTimes.reduce((sum, hours) => sum + hours, 0) / graduationTimes.length : 0;
  const recentGraduations = graduationTimes.filter(hours => hours < 24).length;

  const statistics = {
    total: tokens.length,
    dexscreener_enriched: dexEnrichedCount,
    pumpfun_direct_enriched: pumpfunDirectEnrichedCount,
    triple_enriched: tripleEnrichedCount,
    enrichment_rates: {
      dexscreener: `${((dexEnrichedCount / tokens.length) * 100).toFixed(1)}%`,
      pumpfun_direct: `${((pumpfunDirectEnrichedCount / tokens.length) * 100).toFixed(1)}%`,
      triple: `${((tripleEnrichedCount / tokens.length) * 100).toFixed(1)}%`
    },
    graduation: {
      tokens_with_graduation_data: graduationTimes.length,
      average_graduation_time: `${avgHoursAgo.toFixed(1)} hours ago`,
      recent_graduations_24h: recentGraduations
    },
    activity_distribution: {}
  };

  // Calculate activity distribution
  tokens.forEach(token => {
    const activity = token.activity_level || 'unknown';
    statistics.activity_distribution[activity] = (statistics.activity_distribution[activity] || 0) + 1;
  });

  res.json(createResponse(true, {
    tokens,
    statistics
  }, `Retrieved ${tokens.length} graduated tokens with full triple enrichment`, startTime));
}));

// In-memory cache for categories (hot path)
const categoryCache = {
  'for-you': { data: [], ts: 0 },
  'runners': { data: [], ts: 0 },
  'graduated': { data: [], ts: 0 },
  'featured': { data: [], ts: 0 }
};

const now = () => Date.now();
const TTL = {
  'for-you': 300000,  // 5 minutes
  'runners': 300000,  // 5 minutes
  'featured': 300000, // 5 minutes
  'graduated': 600000 // 10 minutes
};

const isFresh = (name) => (now() - (categoryCache[name]?.ts || 0)) < (TTL[name] || 5000);

// Map enriched token objects to frontend widget shape
const mapToFrontendToken = (t = {}) => ({
  // Identity
  mint: t.tokenAddress || t.mint || t._pumpfun_original?.mint || '',
  name: t.name || t._pumpfun_original?.name || 'Unknown',
  symbol: t.symbol || t._pumpfun_original?.symbol || 'N/A',
  image_uri: t.logo || t.image_uri || t._pumpfun_original?.image_uri || '',

  // Socials and description
  description: t.description || t.pumpfun_description || t._pumpfun_direct_original?.description || '',
  website: t.website || t.pumpfun_website || t._pumpfun_direct_original?.website || '',
  twitter: t.twitter || t.pumpfun_twitter || t._pumpfun_direct_original?.twitter || '',
  telegram: t.telegram || t.pumpfun_telegram || t._pumpfun_direct_original?.telegram || '',

  // Market
  usd_market_cap: t.marketCap ?? t.usd_market_cap ?? null,

  // Supply and progress
  total_supply: t.total_supply || t._pumpfun_direct_original?.total_supply || 0,
  bonding_curve_progress: t.bondingCurveProgress ?? null,

  // Times
  created_timestamp: t.createdAt ? new Date(t.createdAt).getTime() : (t._pumpfun_direct_original?.created_timestamp || null),
  last_trade_timestamp: t._pumpfun_direct_original?.last_trade_timestamp || null,
  last_reply: t._pumpfun_direct_original?.last_reply || null,
  reply_count: t._pumpfun_direct_original?.reply_count || 0,

  // Keep raw for debugging if needed
  _sources: t._data_sources || [],
  _enriched: !!t._enriched
});


// Simple circuit breaker for refresh tasks
const refreshBreaker = {
  forYou: { failures: 0, openUntil: 0 },
  graduated: { failures: 0, openUntil: 0 }
};

const breakerIsOpen = (name) => Date.now() < (refreshBreaker[name]?.openUntil || 0);
const breakerRecordSuccess = (name) => { if (refreshBreaker[name]) { refreshBreaker[name].failures = 0; refreshBreaker[name].openUntil = 0; }};
const breakerRecordFailure = (name) => {
  if (!refreshBreaker[name]) return;
  const b = refreshBreaker[name];
  b.failures += 1;
  if (b.failures >= 3) {
    // Back off for 30s on repeated failures
    b.openUntil = Date.now() + 30000;
    b.failures = 0;
    logJSON('refresh.breaker.open', { task: name, until: new Date(b.openUntil).toISOString() });
  }
};

// PumpFun category utilities and endpoints
const normalizeLimit = (value, fallback = 50, max = 200) => {
  const n = parseInt(value, 10);
  if (Number.isNaN(n) || n <= 0) return fallback;
  return Math.min(n, max);
};

const computeRunners = (tokens, limit) => {
  // Runners: prioritize activity and momentum
  const score = (t) => {
    const tx = (t.total_txns_24h || 0);
    const vol = (t.volume_h1 || t.volume_h24 || 0);
    const pc1h = (t.priceChange_h1 || 0);
    const trending = t.is_trending_up ? 1 : 0;
    return (tx * 2) + vol + (pc1h * 100) + (trending * 500);
  };
  const filtered = tokens.filter(t => ['high','medium'].includes(t.activity_level));
  return filtered.sort((a,b) => score(b) - score(a)).slice(0, limit);
};

const computeFeatured = (tokens, limit) => {
  // Featured: larger caps or notable momentum
  const score = (t) => {
    const mc = Number(t.marketCap || 0);
    const perf = (t.priceChange_h24 || 0) + (t.priceChange_h1 || 0);
    const vol = (t.volume_h24 || 0);
    return (mc * 0.8) + (perf * 100) + (vol * 0.2);
  };
  return [...tokens].sort((a,b) => score(b) - score(a)).slice(0, limit);
};

const getCategoryTokens = async (name, limit) => {
  const l = normalizeLimit(limit);
  const cat = (name || '').toLowerCase();

  if (cat === 'graduated') {
    return await tokenService.getEnrichedGraduatedTokens(l);
  }

  if (cat === 'for-you') {
    return await tokenService.getEnrichedNewTokens(l);
  }

  if (cat === 'runners') {
    const pool = await tokenService.getEnrichedNewTokens(Math.min(l * 2, 100));
    return computeRunners(pool, l);
  }

  if (cat === 'featured') {
    const pool = await tokenService.getEnrichedNewTokens(Math.min(l * 2, 100));
    return computeFeatured(pool, l);
  }

  // Fallback: treat as for-you
  return await tokenService.getEnrichedNewTokens(l);
};

/**
 * @swagger
 * /api/pumpfun/category/{name}:
 *   get:
 *     summary: Get tokens for a specific PumpFun category (for-you, runners, graduated, featured)
 *     parameters:
 *       - in: path
 *         name: name
 *         required: true
 *         schema:
 *           type: string
 *         description: Category name
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *         description: Number of tokens to return (default 50, max 200)
 *     responses:
 *       200:

// Background refreshers (keep cache warm)
const L_DEFAULT = 100;

const logJSON = (event, payload = {}) => {
  try {
    console.log(JSON.stringify({ ts: new Date().toISOString(), event, ...payload }));
  } catch (e) {
    console.log(`[logJSON:${event}]`, payload);
  }
};

async function refreshForYouAndDerived(limit = L_DEFAULT) {
  const l = normalizeLimit(limit, 100, 200);
  if (breakerIsOpen('forYou')) {
    return logJSON('refresh.for-you.skipped.breaker');
  }
  try {
    const rawPool = await tokenService.getEnrichedNewTokens(Math.min(l * 2, 100));
    const runnersRaw = computeRunners(rawPool, l);
    const featuredRaw = computeFeatured(rawPool, l);

    const forYouMapped = rawPool.slice(0, l).map(mapToFrontendToken);
    const runnersMapped = runnersRaw.map(mapToFrontendToken);
    const featuredMapped = featuredRaw.map(mapToFrontendToken);

    // Update in-memory cache for fast responses
    categoryCache['for-you'] = { data: forYouMapped, ts: now() };
    categoryCache['runners'] = { data: runnersMapped, ts: now() };
    categoryCache['featured'] = { data: featuredMapped, ts: now() };

    // Push to Supabase for centralized reads
    upsertTokensToSupabase(rawPool);
    upsertCategoryToSupabase('for-you', forYouMapped.map(t => t.mint));
    upsertCategoryToSupabase('runners', runnersMapped.map(t => t.mint));
    upsertCategoryToSupabase('featured', featuredMapped.map(t => t.mint));

    logJSON('refresh.for-you', { count: forYouMapped.length });
    logJSON('refresh.runners', { count: runnersMapped.length });
    logJSON('refresh.featured', { count: featuredMapped.length });
    breakerRecordSuccess('forYou');
  } catch (e) {
    breakerRecordFailure('forYou');
    logJSON('refresh.for-you.error', { error: e?.message || String(e) });
  }
}

async function refreshGraduated(limit = L_DEFAULT) {
  const l = normalizeLimit(limit, 100, 200);
  if (breakerIsOpen('graduated')) {
    return logJSON('refresh.graduated.skipped.breaker');
  }
  try {
    const raw = await tokenService.getEnrichedGraduatedTokens(l);
    const mapped = raw.map(mapToFrontendToken);
    categoryCache['graduated'] = { data: mapped, ts: now() };

    // Upsert tokens and graduated category
    upsertTokensToSupabase(raw);
    upsertCategoryToSupabase('graduated', mapped.map(t => t.mint));

    logJSON('refresh.graduated', { count: mapped.length });
    breakerRecordSuccess('graduated');
  } catch (e) {
    breakerRecordFailure('graduated');
    logJSON('refresh.graduated.error', { error: e?.message || String(e) });
  }
}

// Kick off initial warmup and intervals
(async () => {
  await Promise.allSettled([
    refreshForYouAndDerived(L_DEFAULT),
    refreshGraduated(L_DEFAULT)
  ]);

  setInterval(() => refreshForYouAndDerived(L_DEFAULT), 5000);
  setInterval(() => refreshGraduated(L_DEFAULT), 15000);

// WebSocket substrate to PumpFun Frontend API v3 (best-effort, resilient)
(function startPumpfunWS() {
  const WS_URL = 'wss://frontend-api-v3.pump.fun';
  let ws = null;
  let reconnectMs = 2000;
  const MAX_RECONNECT = 30000;

  const connect = () => {
    try {
      ws = new WebSocket(WS_URL);

      ws.on('open', () => {
        logJSON('ws.open', { url: WS_URL });
        reconnectMs = 2000;
        // Subscribe if needed; some endpoints emit without subscription
        try {
          ws.send(JSON.stringify({ type: 'subscribe', channel: 'tradeCreated' }));
        } catch {}
      });

      ws.on('message', async (data) => {
        try {
          const msg = JSON.parse(data.toString());
          // We’re interested in events that indicate new tokens or activity
          // Heuristic: when we detect relevant events, refresh hot categories quickly
          if (msg?.type === 'tradeCreated' || msg?.channel === 'tradeCreated') {
            refreshForYouAndDerived(100);
          }
        } catch (e) {
          logJSON('ws.message.error', { error: e?.message || String(e) });
        }
      });

      ws.on('close', () => {
        logJSON('ws.close');
        setTimeout(connect, reconnectMs);
        reconnectMs = Math.min(reconnectMs * 2, MAX_RECONNECT);
      });

      ws.on('error', (err) => {
        logJSON('ws.error', { error: err?.message || String(err) });
        try { ws.close(); } catch {}
      });
    } catch (e) {
      logJSON('ws.connect.error', { error: e?.message || String(e) });
      setTimeout(connect, reconnectMs);
      reconnectMs = Math.min(reconnectMs * 2, MAX_RECONNECT);
    }
  };

  connect();
})();

})();

 *         description: Tokens for the requested category
 */
app.get('/api/pumpfun/category/:name', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  const { name } = req.params;
  const { limit } = req.query;
  const l = normalizeLimit(limit);

  // ALWAYS serve from cache - never trigger fresh API calls from widget requests
  const cached = categoryCache[name]?.data || [];
  const available = cached.slice(0, l);

  if (available.length > 0) {
    return res.json(createResponse(true, { tokens: available }, `Cache served ${available.length}/${l} for ${name}`, startTime));
  }

  // If cache is completely empty, return empty result - background refresh will populate it
  res.json(createResponse(true, { tokens: [] }, `Cache empty for ${name}, background refresh will populate`, startTime));
}));

/**
 * @swagger
 * /api/pumpfun/categories:
 *   get:
 *     summary: Get multiple PumpFun categories at once (for-you, runners, graduated, featured)
 *     parameters:
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *         description: Number of tokens per category (default 50, max 200)
 *     responses:
 *       200:
 *         description: Category buckets with token arrays
 */
app.get('/api/pumpfun/categories', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  const { limit } = req.query;
  const l = normalizeLimit(limit);

  // Cache-first strategy per bucket
  const ensureBucket = async (name, producer) => {
    if (isFresh(name) && (categoryCache[name]?.data?.length || 0) >= l) {
      return categoryCache[name].data.slice(0, l);
    }
    const data = await producer();
    const mapped = data.map(mapToFrontendToken);
    categoryCache[name] = { data: mapped, ts: now() };
    return mapped.slice(0, l);
  };

  // for-you base pool used to compute runners/featured
  const forYouPool = await ensureBucket('for-you', async () => (
    await tokenService.getEnrichedNewTokens(Math.min(l * 2, 100))
  ));

  const [graduated, runners, featured] = await Promise.all([
    ensureBucket('graduated', async () => await tokenService.getEnrichedGraduatedTokens(l)),
    (async () => computeRunners(forYouPool, l))(),
    (async () => computeFeatured(forYouPool, l))()
  ]);

  // Persist runners/featured in cache too
  categoryCache['runners'] = { data: runners, ts: now() };
  categoryCache['featured'] = { data: featured, ts: now() };

  const forYou = forYouPool.slice(0, l);

  res.json(createResponse(true, {
    'for-you': forYou,
    'graduated': graduated,
    'runners': runners,
    'featured': featured
  }, `Retrieved categories with limit ${l}`, startTime));
}));


// Global error handler
app.use((error, req, res, next) => {
  console.error('API Error:', error);

  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';

  res.status(statusCode).json(createResponse(false, message, 'An error occurred while processing your request'));
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json(createResponse(false, 'Endpoint not found', `The requested endpoint ${req.originalUrl} was not found`));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AP3X-PumP Simple Token API Server running on port ${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🌐 Available Endpoints:`);
  console.log(`   GET /api/tokens/new - 50 new tokens with full enrichment (all fields)`);
  console.log(`   GET /api/tokens/bonding - 50 bonding tokens with full enrichment (all fields)`);
  console.log(`   GET /api/tokens/graduated - 50 graduated tokens with full enrichment (all fields)`);
});

export default app;
