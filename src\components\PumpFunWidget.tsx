import { Search, TrendingUp, ArrowLeft, ExternalLink, Twitter, Globe, MessageCircle, Sparkles, Clock, Users, ChevronRight, Info, RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';
import { PriceChart } from './PriceChart';
import { CompactTokenChart } from './CompactTokenChart';
import { pumpFunAPI, type PumpFunTokenResponse, type PumpFunCategory } from '../lib/PumpFunAPI';
import { registerWidgetContext, updateWidgetContext, formatPumpFunContext } from '../lib/widgetContext';
import { usePumpFunWidget } from '../lib/hooks/useWidgetContext';

const TOKEN_LIMITS = {
  DEFAULT: 12,
  MEDIUM: 20,
  LARGE: 40
};

function getTimeAgo(timestamp: number): string {
  if (!timestamp || isNaN(timestamp)) return 'Unknown';

  const now = Date.now();
  // Handle both seconds and milliseconds timestamps
  const timestampMs = timestamp > 1000000000000 ? timestamp : timestamp * 1000;
  const diff = now - timestampMs;

  if (diff < 0) return 'Just now';

  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);
  const months = Math.floor(days / 30);

  if (months > 0) {
    return `${months}M`;
  } else if (weeks > 0) {
    return `${weeks}W`;
  } else if (days > 0) {
    return `${days}D`;
  } else if (hours > 0) {
    return `${hours}H`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return 'Just now';
  }
}

function formatMarketCap(usdMarketCap: number): string {
  if (!usdMarketCap || isNaN(usdMarketCap)) return '$0';

  if (usdMarketCap >= 1000000) {
    return `$${(usdMarketCap / 1000000).toFixed(1)}M`;
  } else if (usdMarketCap >= 1000) {
    return `$${(usdMarketCap / 1000).toFixed(1)}K`;
  } else {
    return `$${usdMarketCap.toFixed(0)}`;
  }
}

function formatVolume(volume: number): string {
  if (!volume || isNaN(volume)) return '$0';

  if (volume >= 1000000) {
    return `$${(volume / 1000000).toFixed(1)}M`;
  } else if (volume >= 1000) {
    return `$${(volume / 1000).toFixed(1)}K`;
  } else {
    return `$${volume.toFixed(0)}`;
  }
}

function formatHolders(holders: number): string {
  if (!holders || isNaN(holders)) return '0';

  if (holders >= 1000000) {
    return `${(holders / 1000000).toFixed(1)}M`;
  } else if (holders >= 1000) {
    return `${(holders / 1000).toFixed(1)}K`;
  } else {
    return holders.toString();
  }
}

function calculateBondingCurveProgress(token: PumpFunToken): number {
  // Check if token is graduated (has Raydium pool)
  if (token.complete || token.raydium_pool) {
    console.log(`🎓 Token ${token.symbol} is graduated - 100% bonding curve progress`);
    return 100; // Already graduated
  }

  // First priority: Use stored bonding progress if available (most accurate)
  if (token.bonding_progress && typeof token.bonding_progress === 'number' && token.bonding_progress > 0) {
    console.log(`🔗 Using stored bonding progress for ${token.symbol}: ${token.bonding_progress.toFixed(2)}%`);
    return Math.max(0, Math.min(token.bonding_progress, 100));
  }

  // Debug logging for fallback calculation
  console.log('Calculating bonding curve for:', {
    mint: token.mint,
    symbol: token.symbol,
    virtual_token_reserves: token.virtual_token_reserves,
    virtual_sol_reserves: token.virtual_sol_reserves,
    usd_market_cap: token.usd_market_cap,
    complete: token.complete,
    bonding_progress: token.bonding_progress
  });

  // Second priority: Use virtual token reserves calculation
  if (token.virtual_token_reserves && !isNaN(token.virtual_token_reserves) && token.virtual_token_reserves > 0) {
    // pump.fun bonding curve calculation based on virtual token reserves
    const INITIAL_VIRTUAL_TOKEN_RESERVES = 1_073_000_000 * 1_000_000; // 1.073B tokens with 6 decimals
    const TOKENS_TO_COLLECT = 793_100_000 * 1_000_000; // 793.1M tokens can be sold from curve

    const tokensCollected = INITIAL_VIRTUAL_TOKEN_RESERVES - token.virtual_token_reserves;
    const progress = (tokensCollected * 100) / TOKENS_TO_COLLECT;

    console.log(`📊 Using virtual reserves calculation for ${token.symbol}: ${progress.toFixed(2)}%`);
    return Math.max(0, Math.min(progress, 100));
  }

  // Third priority: Estimate from market cap (least accurate)
  if (token.usd_market_cap && token.usd_market_cap > 0) {
    // Estimate progress based on market cap (graduation at ~$69K)
    const GRADUATION_MARKET_CAP = 69000;
    const progress = (token.usd_market_cap / GRADUATION_MARKET_CAP) * 100;
    console.log(`💰 Using market cap estimation for ${token.symbol}: ${progress.toFixed(2)}%`);
    return Math.max(0, Math.min(progress, 100));
  }

  return 0;
}

function formatBondingCurveProgress(progress: number): string {
  if (progress >= 100) return '100%';
  if (progress >= 10) return `${progress.toFixed(0)}%`;
  if (progress >= 1) return `${progress.toFixed(1)}%`;
  return `${progress.toFixed(2)}%`;
}



async function fetchPriceHistory(address: string) {
  const now = Math.floor(Date.now() / 1000);
  const oneDayAgo = now - (24 * 60 * 60);

  const options = {
    method: 'GET',
    headers: {
      accept: 'application/json',
      'x-chain': 'solana',
      'X-API-KEY': import.meta.env.VITE_BIRDEYE_API_KEY
    }
  };

  try {
    const response = await fetch(
      `https://public-api.birdeye.so/defi/history_price?address=${address}&address_type=token&type=15m&time_from=${oneDayAgo}&time_to=${now}`,
      options
    );
    const data = await response.json();
    return data.data?.items || [];
  } catch (error) {
    console.error('Error fetching price history:', error);
    return [];
  }
}

interface PumpFunToken {
  mint: string;
  name: string;
  symbol: string;
  description: string;
  image_uri: string;
  metadata_uri: string;
  twitter: string | null;
  website: string | null;
  telegram: string | null;
  bonding_curve: string;
  associated_bonding_curve: string;
  creator: string;
  created_timestamp: number;
  raydium_pool: string | null;
  complete: boolean;
  virtual_sol_reserves: number;
  virtual_token_reserves: number;
  total_supply: number;
  show_name: boolean;
  last_trade_timestamp: number;
  king_of_the_hill_timestamp: number | null;
  market_cap: number;
  reply_count: number;
  last_reply: number;
  nsfw: boolean;
  market_id: string | null;
  inverted: boolean;
  is_currently_live: boolean;
  username: string | null;
  profile_image: string | null;
  usd_market_cap: number;
  is_banned?: boolean;
  hidden?: boolean;
  initialized?: boolean;
  video_uri?: string | null;
  banner_uri?: string | null;
  // Enhanced fields from advanced APIs
  volume?: number;
  numHolders?: number;
  graduationTime?: number;
  bonding_progress?: number; // Moralis bonding curve progress (0-100)
  // Real-time trading data (optional integration)
  realtime_volume?: number;
  realtime_trader_count?: number;
  realtime_buy_sell_ratio?: number;
  realtime_last_trade?: number;
}

interface FeatureFlags {
  trending_carousel_enabled: boolean;
  semantic_search_enabled: boolean;
  similar_coins_enabled: boolean;
  trade_history_recs_enabled: boolean;
  multi_column_advanced_enabled: boolean;
  hybrid_search_enabled: boolean;
  search_ranked_enabled: boolean;
  homepage_v2_enabled: boolean;
  livestreams_enabled: boolean;
  create_coin_v2_enabled: boolean;
  [key: string]: boolean;
}

// Optional real-time data integration
interface RealtimeTokenData {
  mint: string;
  volume: number;
  trader_count: number;
  buy_sell_ratio: number;
  last_trade_time: number;
}

interface PumpFunWidgetProps {
  isActive: boolean;
  realtimeData?: Map<string, RealtimeTokenData>;
  enableRealtimeIntegration?: boolean;
}

// Helper function to get a working image URL with IPFS fallbacks
const getImageUrl = (imageUrl: string, tokenSymbol: string = '?') => {
  if (!imageUrl) {
    return `data:image/svg+xml,${encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#111"/><text x="100" y="100" text-anchor="middle" dominant-baseline="middle" fill="#22c55e" font-size="24" font-family="Arial">${tokenSymbol}</text></svg>`)}`;
  }

  // If it's an IPFS URL, try multiple gateways
  if (imageUrl.includes('ipfs/')) {
    const ipfsHash = imageUrl.split('ipfs/')[1];
    // Try different IPFS gateways in order of reliability
    const gateways = [
      `https://ipfs.io/ipfs/${ipfsHash}`,
      `https://gateway.pinata.cloud/ipfs/${ipfsHash}`,
      `https://cloudflare-ipfs.com/ipfs/${ipfsHash}`,
      imageUrl // Original URL as last resort
    ];
    return gateways[0]; // Start with most reliable gateway
  }

  return imageUrl;
};

export function PumpFunWidget({
  isActive,
  realtimeData,
  enableRealtimeIntegration = false
}: PumpFunWidgetProps) {
  const [tokens, setTokens] = useState<PumpFunToken[]>([]);

  // 🚀 AGENT INTEGRATION: Auto-register widget and sync data
  const widgetIntegration = usePumpFunWidget('default', {
    autoRegister: true,
    syncInterval: 5000 // Sync every 5 seconds
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedToken, setSelectedToken] = useState<PumpFunToken | null>(null);

  // Generate unique widget ID for context tracking
  const [widgetId] = useState(() => `pumpfun_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`);
  const [threadId] = useState(() => {
    // Try to get thread ID from URL or context - for now use a default
    return window.location.hash.includes('thread_')
      ? window.location.hash.split('thread_')[1]?.split('/')[0] || 'default'
      : 'default';
  });
  const [error, setError] = useState<string | null>(null);
  const [priceHistory, setPriceHistory] = useState<Array<{ date: number, value: number }>>([]);
  const [isLoadingPrice, setIsLoadingPrice] = useState(false);
  const [chartType, setChartType] = useState<'custom' | 'gmgn'>(() => {
    // Load saved preference from localStorage
    const saved = localStorage.getItem('pumpfun-chart-type');
    return (saved === 'gmgn' || saved === 'custom') ? saved : 'gmgn'; // Default to GMGN for better pump.fun data
  });

  // Handle chart type change and save preference
  const handleChartTypeChange = (type: 'custom' | 'gmgn') => {
    setChartType(type);
    localStorage.setItem('pumpfun-chart-type', type);
  };
  const [displayLimit, setDisplayLimit] = useState(TOKEN_LIMITS.DEFAULT);
  const [viewMode, setViewMode] = useState<PumpFunCategory>('for-you');

  // Cache for all categories - pre-fetch and store in memory
  const [categoryCache, setCategoryCache] = useState<{
    [key in PumpFunCategory]?: PumpFunToken[]
  }>({});

  // Track which categories are currently being fetched
  const [fetchingCategories, setFetchingCategories] = useState<Set<PumpFunCategory>>(new Set());

  // Track background refresh state
  const [isBackgroundRefreshing, setIsBackgroundRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState<'marketCap' | 'volume' | 'holders' | 'creationTime'>('marketCap');
  // Feature flags (using defaults since we removed cache server)
  const featureFlags = {
    showVolume: true,
    showHolders: true,
    showProgress: true,
    showSocials: true
  };

  // Normalize token from various API shapes (Supabase vs Ingestion)
  const normalizeApiToken = (token: any): PumpFunToken => {
    const mint = token.mint || token.tokenAddress || token._pumpfun_original?.mint || '';
    const name = token.name || token._pumpfun_original?.name || '';
    const symbol = token.symbol || token._pumpfun_original?.symbol || '';
    const image_uri = token.image_url || token.image_uri || token.logo || token._pumpfun_original?.image_uri || '';
    const website = token.website_url || token.website || token.pumpfun_website || token._pumpfun_direct_original?.website || null;
    const twitter = token.twitter_url || token.twitter || token.pumpfun_twitter || token._pumpfun_direct_original?.twitter || null;
    const telegram = token.telegram_url || token.telegram || token.pumpfun_telegram || token._pumpfun_direct_original?.telegram || null;
    const description = token.description || token.curated_description || token.pumpfun_description || token._pumpfun_direct_original?.description || '';
    const created_at_iso = token.created_at || token.created_timestamp || token._pumpfun_direct_original?.created_timestamp || null;
    const created_timestamp = created_at_iso ? (typeof created_at_iso === 'number' ? created_at_iso : new Date(created_at_iso).getTime()) : 0;
    const usd_market_cap = token.current_market_cap_usd || token.initial_market_cap_usd || token.usd_market_cap || token.marketCap || 0;
    const raydium_pool = token.raydium_pool || token._pumpfun_direct_original?.raydium_pool || null;
    const bonding_curve_progress = token.bonding_progress ?? token.bonding_curve_progress ?? token.bondingCurveProgress ?? (raydium_pool ? 100 : null);

    return {
      mint,
      name,
      symbol,
      description,
      image_uri,
      metadata_uri: token.metadata_url || token.metadata_uri || '',
      twitter,
      website,
      telegram,
      bonding_curve: token.bonding_curve_address || token.pumpfun_bonding_curve || token.bonding_curve || '',
      associated_bonding_curve: token.associated_bonding_curve || token.pumpfun_associated_bonding_curve || '',
      creator: token.creator || token.pumpfun_creator || '',
      created_timestamp,
      raydium_pool,
      complete: Boolean(raydium_pool) || Boolean(token.complete),
      virtual_sol_reserves: token.virtual_sol_reserves || 0,
      virtual_token_reserves: token.virtual_token_reserves || 0,
      total_supply: token.total_supply || 0,
      show_name: true,
      last_trade_timestamp: token.last_trade_timestamp || 0,
      king_of_the_hill_timestamp: token.king_of_the_hill_timestamp || null,
      market_cap: usd_market_cap,
      reply_count: token.reply_count || 0,
      last_reply: token.last_reply || 0,
      nsfw: Boolean(token.is_nsfw || token.nsfw),
      market_id: token.market_id || null,
      inverted: Boolean(token.inverted),
      is_currently_live: Boolean(token.is_currently_live),
      username: token.username || null,
      profile_image: token.profile_image || null,
      usd_market_cap,
      is_banned: Boolean(token.is_banned),
      hidden: Boolean(token.hidden),
      initialized: true,
      video_uri: token.video_url || token.video_uri || null,
      banner_uri: token.banner_url || token.banner_uri || null,
      volume: token.volume || 0,
      numHolders: token.numHolders || 0,
      bonding_progress: bonding_curve_progress,
      graduationTime: token.graduationTime || (raydium_pool ? (token.updated_at ? new Date(token.updated_at).getTime() : null) : null)
    };
  };



  // Pre-fetch all categories and cache them in memory
  const prefetchAllCategories = async (forceRefresh = false) => {
    if (!isActive && !forceRefresh) {
      console.log('⚠️ PumpFun widget is not active, skipping pre-fetch');
      return;
    }

    if (forceRefresh) {
      console.log('🔄 Background refreshing all PumpFun categories with latest data...');
      setIsBackgroundRefreshing(true);
    } else {
      console.log('🚀 Pre-fetching all PumpFun categories...');
    }

    const categories: PumpFunCategory[] = ['for-you', 'runners', 'graduated', 'featured'];
    const newFetchingSet = new Set(categories);
    setFetchingCategories(newFetchingSet);

    try {
      // Fetch all categories in parallel
      const categoryPromises = categories.map(async (category) => {
        try {
          console.log(`📥 Fetching ${category} category...`);
          const tokens = await pumpFunAPI.getRecentTokensByCategory(category, 100);

          // Normalize tokens from any API shape (ingestion or legacy)
          const processedTokens: PumpFunToken[] = tokens.map(normalizeApiToken);

          console.log(`✅ Successfully cached ${processedTokens.length} tokens for ${category}`);
          return { category, tokens: processedTokens };
        } catch (error) {
          console.error(`❌ Failed to fetch ${category}:`, error);
          return { category, tokens: [] };
        }
      });

      // Wait for all categories to complete
      const results = await Promise.all(categoryPromises);

      // Update cache with all results
      const newCache: { [key in PumpFunCategory]?: PumpFunToken[] } = {};
      results.forEach(({ category, tokens }) => {
        newCache[category] = tokens;
        console.log(`📦 Cached ${category}: ${tokens.length} tokens`);
      });

      setCategoryCache(prev => ({ ...prev, ...newCache }));

      // Set the default category tokens (For You)
      const forYouTokens = newCache['for-you'] || [];
      setTokens(forYouTokens);

      if (forceRefresh) {
        console.log('🔄 Background refresh complete! Updated cache with latest data:', {
          'for-you': newCache['for-you']?.length || 0,
          'runners': newCache['runners']?.length || 0,
          'graduated': newCache['graduated']?.length || 0,
          'featured': newCache['featured']?.length || 0
        });
      } else {
        console.log('🎉 All categories pre-fetched and cached!', {
          'for-you': newCache['for-you']?.length || 0,
          'runners': newCache['runners']?.length || 0,
          'graduated': newCache['graduated']?.length || 0,
          'featured': newCache['featured']?.length || 0
        });
      }

      setError(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to pre-fetch categories';
      console.error('❌ Error during pre-fetch:', errorMessage);
      setError('Unable to load token categories. Please try refreshing.');
    } finally {
      setFetchingCategories(new Set());
      setIsBackgroundRefreshing(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (selectedToken) {
      if (!isActive) return;

      setIsLoadingPrice(true);
      fetchPriceHistory(selectedToken.mint)
        .then(items => {
          setPriceHistory(items.map((item: any) => ({
            date: item.unixTime * 1000, // Convert to milliseconds
            value: item.value
          })));
        })
        .finally(() => setIsLoadingPrice(false));
    }
  }, [selectedToken, isActive]);

  // Switch to a cached category instantly, or fetch if not cached
  const switchToCategory = (category: PumpFunCategory) => {
    console.log(`🔄 Switching to ${category} category...`);
    console.log(`🔍 Current cache state:`, Object.keys(categoryCache).map(key => `${key}: ${categoryCache[key as PumpFunCategory]?.length || 0} tokens`));

    // Check if we have this category cached
    const cachedTokens = categoryCache[category];

    if (cachedTokens && cachedTokens.length > 0) {
      console.log(`⚡ Using cached ${category} tokens (${cachedTokens.length} tokens)`);
      setTokens(cachedTokens);
      setError(null);
      setViewMode(category);
    } else if (fetchingCategories.has(category)) {
      console.log(`⏳ ${category} is currently being fetched...`);
      setViewMode(category);
      setIsLoading(true);
    } else {
      console.log(`📥 ${category} not cached, fetching...`);
      setViewMode(category);
      fetchSingleCategory(category);
    }
  };

  // Fetch a single category (fallback for cache misses)
  const fetchSingleCategory = async (category: PumpFunCategory) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`🔗 Fetching ${category} tokens from ingestion API...`);
      const tokens = await pumpFunAPI.getRecentTokensByCategory(category, 100);

      if (!Array.isArray(tokens)) {
        throw new Error(`Invalid response format for ${category} tokens`);
      }

      if (tokens.length === 0) {
        console.warn(`⚠️ No tokens found for ${category} category`);
        setTokens([]);
        setCategoryCache(prev => ({ ...prev, [category]: [] }));
        return;
      }

      // Normalize tokens (same logic as pre-fetch)
      const processedTokens: PumpFunToken[] = tokens.map(normalizeApiToken);

      setTokens(processedTokens);
      setCategoryCache(prev => ({ ...prev, [category]: processedTokens }));
      setError(null);
      console.log(`✅ Successfully fetched and cached ${processedTokens.length} ${category} tokens`);

      // 🚀 AGENT INTEGRATION: Sync token data with agent
      widgetIntegration.updateData({
        category,
        viewMode: 'grid',
        tokenCount: processedTokens.length,
        topTokens: processedTokens.slice(0, 10).map(token => ({
          symbol: token.symbol,
          name: token.name,
          mint: token.mint,
          marketCap: token.market_cap,
          price: token.usd_market_cap,
          bondingCurveProgress: token.bonding_curve_progress,
          description: token.description?.substring(0, 100) || 'No description',
          website: token.website,
          twitter: token.twitter,
          telegram: token.telegram,
          isGraduated: token.complete || false,
          createdAt: token.created_timestamp,
          volume24h: token.volume_24h,
          holders: token.holder_count
        })),
        totalMarketCap: processedTokens.reduce((sum, token) => sum + (token.market_cap || 0), 0),
        lastUpdated: Date.now()
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      console.error(`❌ Error fetching ${category} tokens:`, errorMessage);
      setError(`Unable to load ${category} tokens. Please try refreshing.`);
      setTokens([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Pre-fetch all categories on initial load or when widget becomes active
  useEffect(() => {
    console.log('🔄 PumpFun useEffect triggered:', { isActive, viewMode });
    if (!isActive) {
      console.log('⚠️ Widget not active, not pre-fetching');
      return;
    }
    console.log('✅ Widget is active, pre-fetching all categories...');

    // Initial pre-fetch with cached data for instant display
    prefetchAllCategories().then(() => {
      // After initial load, refresh with fresh data in the background
      console.log('🔄 Initial pre-fetch complete, refreshing with latest data...');
      setTimeout(() => {
        prefetchAllCategories(true); // Force refresh for up-to-date data
      }, 1000); // Small delay to let initial render complete
    });
  }, [isActive]); // Only trigger on active state change

  // Register widget context and update when data changes
  useEffect(() => {
    if (isActive && tokens.length > 0) {
      const contextData = formatPumpFunContext(tokens, viewMode, 'grid');

      // Register widget on first load
      if (tokens.length > 0) {
        registerWidgetContext(widgetId, 'pumpfun', threadId, contextData)
          .then(() => {
            console.log('📡 PumpFun widget context registered:', {
              widgetId,
              threadId,
              tokenCount: tokens.length,
              category: viewMode
            });
          })
          .catch(err => console.warn('⚠️ Failed to register widget context:', err));
      }
    }
  }, [isActive, tokens, viewMode, widgetId, threadId]);

  // Update context when user switches categories
  useEffect(() => {
    if (isActive && tokens.length > 0) {
      const contextData = formatPumpFunContext(tokens, viewMode, 'grid');
      updateWidgetContext(widgetId, contextData)
        .then(() => {
          console.log('📡 PumpFun widget context updated:', {
            category: viewMode,
            tokenCount: tokens.length
          });
        })
        .catch(err => console.warn('⚠️ Failed to update widget context:', err));
    }
  }, [viewMode, tokens, widgetId]);

  // Test Supabase API connectivity on mount
  useEffect(() => {
    const testAPI = async () => {
      try {
        console.log('🧪 Testing Supabase API connectivity...');

        // Test all categories to see which ones work
        const categories: PumpFunCategory[] = ['runners', 'for-you', 'graduated', 'featured'];
        for (const category of categories) {
          console.log(`🔍 Testing ${category} category...`);
          const result = await pumpFunAPI.debugCategory(category);
          console.log(`📊 ${category} result:`, result);

          // Special focus on trending
          if (category === 'trending') {
            console.log(`🔥 TRENDING DETAILED DEBUG:`, {
              success: result.success,
              tokenCount: result.tokens?.length || 0,
              rawDataCount: result.rawData?.length || 0,
              error: result.error,
              sampleRawData: result.rawData?.slice(0, 1)
            });
          }
        }

        const isConnected = await pumpFunAPI.testConnection();
        if (isConnected) {
          console.log('✅ Supabase API connection test successful');
        } else {
          console.warn('⚠️ Supabase API connection test failed');
        }
      } catch (error) {
        console.error('❌ Supabase API test failed:', error);
      }
    };
    testAPI();
  }, []);

  // Scroll to top when entering token detail view and update context
  useEffect(() => {
    if (selectedToken) {
      // Small delay to ensure the component has rendered
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);

      // Update widget context with selected token info
      const contextData = formatPumpFunContext(tokens, viewMode, 'detail');
      const enhancedContext = {
        ...contextData,
        selectedToken: {
          symbol: selectedToken.symbol,
          name: selectedToken.name,
          mint: selectedToken.mint,
          marketCap: selectedToken.market_cap,
          bondingCurveProgress: selectedToken.bonding_curve_progress,
          description: selectedToken.description
        }
      };

      updateWidgetContext(widgetId, enhancedContext)
        .then(() => {
          console.log('📡 PumpFun widget context updated with selected token:', selectedToken.symbol);
        })
        .catch(err => console.warn('⚠️ Failed to update widget context:', err));
    }
  }, [selectedToken, tokens, viewMode, widgetId]);

  const filteredTokens = tokens.filter(token =>
    token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    token.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  ).sort((a, b) => (b.usd_market_cap || 0) - (a.usd_market_cap || 0));

  if (selectedToken) {
    return (
      <div
        className="bg-[#111] rounded-2xl p-4 sm:p-6 lg:p-8 w-full max-w-[95%] sm:max-w-[90%] mx-auto overflow-hidden"
        ref={(el) => {
          if (el) {
            el.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }}
      >
        <div className="flex items-center gap-3 mb-4 sm:mb-8">
          <button
            onClick={() => setSelectedToken(null)}
            className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200 group hover:scale-105 hover:shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 group-hover:translate-x-[-2px] transition-transform" />
          </button>
          <div className="flex items-center gap-3">
            <img
              src={getImageUrl(selectedToken.image_uri, selectedToken.symbol)}
              alt={selectedToken.name}
              className="w-9 h-9 rounded-lg object-cover ring-1 ring-[#181818]"
              onError={(e) => {
                e.currentTarget.src = `data:image/svg+xml,${encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#111"/><text x="100" y="100" text-anchor="middle" dominant-baseline="middle" fill="#22c55e" font-size="24" font-family="Arial">${selectedToken.symbol}</text></svg>`)}`;
              }}
            />
            <div>
              <h2 className="text-lg font-semibold flex items-center gap-2">
                {selectedToken.name}
                <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">
                  {selectedToken.symbol}
                </span>
              </h2>
              <p className="text-xs text-[#666]">
                {selectedToken.mint.slice(0, 8)}...{selectedToken.mint.slice(-6)}
              </p>
            </div>
          </div>
          <div className="ml-auto">
            <a
              href={`https://solscan.io/token/${selectedToken.mint}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-[#666] hover:text-[#22c55e] transition-all duration-200 flex items-center gap-2 bg-[#111] hover:bg-[#22c55e]/5 px-3 py-1.5 rounded-lg hover:scale-105 hover:shadow-lg"
            >
              View on Solscan
              <ExternalLink size={14} />
            </a>
          </div>
        </div>

        {/* Chart Section - Full Width */}
        <div className="space-y-4">
          {/* Chart Type Toggle */}
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-white flex items-center gap-2">
              <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
              Price Chart
            </h3>
            <div className="flex items-center gap-2 bg-[#0A0A0A] rounded-lg p-1">
              <button
                onClick={() => handleChartTypeChange('custom')}
                className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                  chartType === 'custom'
                    ? 'bg-[#22c55e] text-black'
                    : 'text-[#666] hover:text-white hover:bg-[#222]'
                }`}
              >
                Custom
              </button>
              <button
                onClick={() => handleChartTypeChange('gmgn')}
                className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                  chartType === 'gmgn'
                    ? 'bg-[#22c55e] text-black'
                    : 'text-[#666] hover:text-white hover:bg-[#222]'
                }`}
              >
                GMGN
              </button>
            </div>
          </div>

          {/* Chart Display */}
          {chartType === 'custom' ? (
            <PriceChart
              data={priceHistory}
              height="600px"
              title="Price Chart"
              subtitle={isLoadingPrice ? "Loading..." : "24h Price History"}
              isLoading={isLoadingPrice}
            />
          ) : (
            <CompactTokenChart
              tokenAddress={selectedToken.mint}
              tokenName={selectedToken.name}
              tokenSymbol={selectedToken.symbol}
              chain="sol"
              height="600px"
              title="Live Chart"
              subtitle="Real-time data from GMGN.cc"
            />
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mt-8">
          {/* Left Column - Token Info */}
          <div className="lg:col-span-2 space-y-8">
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6 bg-[#0A0A0A] rounded-xl p-4 sm:p-6 hover:ring-1 hover:ring-[#222] transition-all duration-200 group">
              <img
                src={getImageUrl(selectedToken.image_uri, selectedToken.symbol)}
                alt={selectedToken.name}
                className="w-20 h-20 sm:w-24 sm:h-24 rounded-lg object-cover ring-1 ring-[#181818] group-hover:ring-[#22c55e] transition-all duration-200 shadow-lg group-hover:shadow-[#22c55e]/10 group-hover:scale-105"
                onError={(e) => {
                  e.currentTarget.src = `data:image/svg+xml,${encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#111"/><text x="100" y="100" text-anchor="middle" dominant-baseline="middle" fill="#22c55e" font-size="24" font-family="Arial">${selectedToken.symbol}</text></svg>`)}`;
                }}
              />
              <div className="text-center sm:text-left flex-1">
                <h3 className="text-xl sm:text-3xl font-semibold mb-2 bg-gradient-to-r from-white to-gray-400 text-transparent bg-clip-text">{selectedToken.name}</h3>
                <div className="flex items-center gap-3 mb-4 text-sm">
                  <div className="flex items-center gap-1.5">
                    <Clock size={14} className="text-[#666]" />
                    <span className="text-[#666]">
                      {getTimeAgo(selectedToken.last_trade_timestamp)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <Users size={14} className="text-[#666]" />
                    <span className="text-[#666]">
                      {selectedToken.reply_count} replies
                    </span>
                  </div>
                </div>
                <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2 sm:gap-4">
                  {selectedToken.twitter && (
                    <a
                      href={`https://${selectedToken.twitter}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-[#666] hover:text-[#1DA1F2] transition-all duration-200 bg-[#111] hover:bg-[#1DA1F2]/5 px-3 py-1.5 rounded-lg text-sm hover:scale-105 hover:shadow-lg"
                    >
                      <Twitter size={16} />
                      <span>Twitter</span>
                    </a>
                  )}
                  {selectedToken.website && (
                    <a
                      href={selectedToken.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-[#666] hover:text-[#22c55e] transition-all duration-200 bg-[#111] hover:bg-[#22c55e]/5 px-3 py-1.5 rounded-lg text-sm hover:scale-105 hover:shadow-lg"
                    >
                      <Globe size={16} />
                      <span>Website</span>
                    </a>
                  )}
                  {selectedToken.telegram && (
                    <a
                      href={`https://t.me/${selectedToken.telegram}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-[#666] hover:text-[#229ED9] transition-all duration-200 bg-[#111] hover:bg-[#229ED9]/5 px-3 py-1.5 rounded-lg text-sm hover:scale-105 hover:shadow-lg"
                    >
                      <MessageCircle size={16} />
                      <span>Telegram</span>
                    </a>
                  )}
                </div>
              </div>
            </div>

            <div className="bg-[#0A0A0A] rounded-xl p-4 sm:p-6 hover:ring-1 hover:ring-[#222] transition-all duration-200">
              <h4 className="font-medium mb-4 flex items-center gap-2">
                <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                About
              </h4>
              <p className="text-sm text-[#888] leading-relaxed whitespace-pre-line bg-[#111] p-4 rounded-lg">{selectedToken.description}</p>
            </div>

            <div className="bg-[#0A0A0A] rounded-xl p-4 sm:p-6 hover:ring-1 hover:ring-[#222] transition-all duration-200">
              <h4 className="font-medium mb-4 flex items-center gap-2">
                <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                Market Data
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-[#111] p-4 rounded-lg">
                  <div className="text-sm text-[#666] mb-1">Market Cap</div>
                  <div className="font-medium text-lg bg-gradient-to-r from-[#22c55e] to-emerald-400 text-transparent bg-clip-text">
                    {formatMarketCap(selectedToken.usd_market_cap || 0)}
                  </div>
                </div>
                <div className="bg-[#111] p-4 rounded-lg">
                  <div className="text-sm text-[#666] mb-1">Total Supply</div>
                  <div className="font-medium text-lg">
                    {selectedToken.total_supply.toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-[#0A0A0A] rounded-xl p-4 sm:p-6 hover:ring-1 hover:ring-[#222] transition-all duration-200">
              <div className="flex items-center justify-between mb-6">
                <h4 className="font-medium flex items-center gap-2">
                  <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                  Contract Details
                </h4>
                <a
                  href={`https://solscan.io/token/${selectedToken.mint}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-[#666] hover:text-[#22c55e] transition-all duration-200 flex items-center gap-2 bg-[#111] hover:bg-[#22c55e]/5 px-3 py-1.5 rounded-lg hover:scale-105 hover:shadow-lg"
                >
                  View on Solscan
                  <ExternalLink size={14} />
                </a>
              </div>
              <div className="text-sm font-mono text-[#666] break-all bg-[#111] p-4 rounded-lg hover:text-white transition-colors duration-200 group cursor-copy" onClick={() => navigator.clipboard.writeText(selectedToken.mint)}>
                {selectedToken.mint}
                <div className="text-xs mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-[#22c55e]">
                  Click to copy
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Activity */}
          <div className="space-y-8">
            <div className="bg-[#0A0A0A] rounded-xl p-6 hover:ring-1 hover:ring-[#22c55e] transition-all duration-200">
              <h4 className="font-medium mb-6 flex items-center gap-2">
                <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                Activity
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-[#666] flex items-center gap-2">
                    <Sparkles size={14} />
                    Created
                  </div>
                  <div className="text-sm bg-[#111] px-3 py-1 rounded-lg hover:bg-[#181818] transition-colors duration-200 flex items-center gap-2">
                    <span>{getTimeAgo(selectedToken.created_timestamp)}</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-[#666] flex items-center gap-2">
                    <Clock size={14} />
                    Last Trade
                  </div>
                  <div className="text-sm bg-[#111] px-3 py-1 rounded-lg hover:bg-[#181818] transition-colors duration-200">
                    {getTimeAgo(selectedToken.last_trade_timestamp)}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-[#666] flex items-center gap-2">
                    <MessageCircle size={14} />
                    Replies
                  </div>
                  <div className="text-sm bg-[#111] px-3 py-1 rounded-lg hover:bg-[#181818] transition-colors duration-200">
                    {selectedToken.reply_count}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-[#666] flex items-center gap-2">
                    <Users size={14} />
                    Last Reply
                  </div>
                  <div className="text-sm bg-[#111] px-3 py-1 rounded-lg hover:bg-[#181818] transition-colors duration-200">
                    {getTimeAgo(selectedToken.last_reply)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[95%] sm:max-w-[90%] mx-auto overflow-hidden">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="relative">
          <img
            src="https://upload.wikimedia.org/wikipedia/en/b/bd/Pump_fun_logo.png"
            alt="PumpFun"
            className="w-8 h-8 rounded-lg ring-1 ring-[#181818] hover:ring-[#22c55e] transition-colors"
          />
          </div>
          <h2 className="text-lg font-semibold">PumpFun Terminal</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                console.log('🔄 Manual refresh clicked, isActive:', isActive);
                prefetchAllCategories(true); // Force refresh all categories
              }}
              disabled={isLoading}
              className="p-2 text-[#666] hover:text-[#22c55e] hover:bg-[#181818] rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95"
              title="Refresh all categories"
            >
              <RefreshCw size={16} className={`${isLoading || isBackgroundRefreshing ? 'animate-spin' : ''}`} />
            </button>
            {isBackgroundRefreshing && (
              <div className="text-xs bg-blue-500/10 text-blue-500 px-2 py-1 rounded-full flex items-center gap-1">
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                Updating
              </div>
            )}
            {!isActive && (
              <div className="text-xs bg-orange-500/10 text-orange-500 px-2 py-1 rounded-full">
                Inactive
              </div>
            )}
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex bg-[#0A0A0A] rounded-lg p-1 overflow-x-auto">
          <button
            onClick={() => switchToCategory('for-you')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all duration-200 whitespace-nowrap ${
              viewMode === 'for-you'
                ? 'bg-[#22c55e] text-black font-medium'
                : 'text-[#666] hover:text-white hover:bg-[#181818]'
            }`}
          >
            ✨ For You
          </button>

          <button
            onClick={() => switchToCategory('runners')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all duration-200 whitespace-nowrap ${
              viewMode === 'runners'
                ? 'bg-[#22c55e] text-black font-medium'
                : 'text-[#666] hover:text-white hover:bg-[#181818]'
            }`}
          >
            🏃 Runners
          </button>

          <button
            onClick={() => switchToCategory('graduated')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all duration-200 whitespace-nowrap ${
              viewMode === 'graduated'
                ? 'bg-[#22c55e] text-black font-medium'
                : 'text-[#666] hover:text-white hover:bg-[#181818]'
            }`}
          >
            🎓 Graduated
          </button>
          <button
            onClick={() => switchToCategory('featured')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all duration-200 whitespace-nowrap ${
              viewMode === 'featured'
                ? 'bg-[#22c55e] text-black font-medium'
                : 'text-[#666] hover:text-white hover:bg-[#181818]'
            }`}
          >
            ⭐ Featured
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="relative animate-fade-in">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-[#666] w-4 h-4" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-[#0A0A0A] rounded-xl py-3 pl-11 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-[#22c55e] placeholder-[#666] transition-all duration-200 hover:bg-[#111]"
            placeholder="Search tokens..."
          />
        </div>

        <div className="flex items-center gap-6 px-2 animate-fade-in">
          <div className="flex items-center gap-2 text-[#22c55e] bg-[#22c55e]/5 px-3 py-1.5 rounded-lg">
            <TrendingUp size={16} />
            <span className="text-sm font-medium">Showing top {displayLimit}</span>
          </div>
          <button
            onClick={() => setDisplayLimit(TOKEN_LIMITS.DEFAULT)}
            className={`text-sm transition-all duration-200 hover:scale-105 ${
              displayLimit === TOKEN_LIMITS.DEFAULT ? 'text-white' : 'text-[#666] hover:text-white'
            }`}
          >
            Top {TOKEN_LIMITS.DEFAULT}
          </button>
          <button
            onClick={() => setDisplayLimit(TOKEN_LIMITS.MEDIUM)}
            className={`text-sm transition-all duration-200 hover:scale-105 ${
              displayLimit === TOKEN_LIMITS.MEDIUM ? 'text-white' : 'text-[#666] hover:text-white'
            }`}
          >
            Top {TOKEN_LIMITS.MEDIUM}
          </button>
          <button
            onClick={() => setDisplayLimit(TOKEN_LIMITS.LARGE)}
            className={`text-sm transition-all duration-200 hover:scale-105 ${
              displayLimit === TOKEN_LIMITS.LARGE ? 'text-white' : 'text-[#666] hover:text-white'
            }`}
          >
            Top {TOKEN_LIMITS.LARGE}
          </button>
        </div>
      </div>

      {/* Token Grid */}
      <div className="mt-6 animate-fade-in">
        {isLoading ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-[#0A0A0A] rounded-xl flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-8 rounded-full border-2 border-[#22c55e] border-t-transparent animate-spin" />
            </div>
            <h3 className="text-lg font-medium mb-2">Loading Tokens</h3>
            <p className="text-sm text-[#666]">
              Fetching latest token data...
            </p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-[#0A0A0A] rounded-xl flex items-center justify-center mx-auto mb-4">
              <Info className="w-8 h-8 text-red-500" />
            </div>
            <h3 className="text-lg font-medium mb-2">Error Loading Tokens</h3>
            <p className="text-sm text-[#666]">{error}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            {filteredTokens.slice(0, displayLimit).map((token) => (
              <div
                key={token.mint}
                className="bg-[#0A0A0A] rounded-xl overflow-hidden transition-all duration-200 cursor-pointer group hover:shadow-lg hover:shadow-[#22c55e]/20 flex flex-col border border-transparent hover:border-[#22c55e]"
                onClick={() => setSelectedToken(token)}
              >
                <div className="relative aspect-square bg-gradient-to-br from-[#111] to-[#222] overflow-hidden flex-shrink-0">
                  <img
                    src={getImageUrl(token.image_uri, token.symbol)}
                    alt={token.name}
                    className="w-full h-full object-cover transition-all duration-200 group-hover:brightness-110"
                    onError={(e) => {
                      e.currentTarget.src = `data:image/svg+xml,${encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#111"/><text x="100" y="100" text-anchor="middle" dominant-baseline="middle" fill="#22c55e" font-size="24" font-family="Arial">${token.symbol || '?'}</text></svg>`)}`;
                    }} />

                  {/* Graduated Badge */}
                  {token.complete && token.raydium_pool && (
                    <div className="absolute top-2 right-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg">
                      🎓 Graduated
                    </div>
                  )}

                  {/* Live Badge */}
                  {token.is_currently_live && (
                    <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg animate-pulse">
                      🔴 LIVE
                    </div>
                  )}

                  {/* NSFW Badge */}
                  {token.nsfw && (
                    <div className="absolute bottom-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg">
                      ⚠️ NSFW
                    </div>
                  )}

                  {/* Banned Badge */}
                  {token.is_banned && (
                    <div className="absolute bottom-2 left-2 bg-red-600 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg">
                      🚫 Banned
                    </div>
                  )}

                  <div className="absolute inset-0 bg-gradient-to-t from-[#0A0A0A] to-transparent opacity-90"></div>
                </div>

                <div className="p-4 flex flex-col flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium truncate mb-1" title={token.name}>{token.name}</h3>
                      <div className="text-sm text-[#22c55e] truncate">{token.symbol}</div>
                    </div>
                    <ChevronRight size={16} className="text-[#666] group-hover:text-[#22c55e] transition-colors group-hover:translate-x-1 ml-2 flex-shrink-0" />
                  </div>

                  <div className="grid grid-cols-2 gap-2 mt-4">
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Market Cap</div>
                      <div className="font-medium truncate text-[#22c55e]">{formatMarketCap(token.usd_market_cap || 0)}</div>
                    </div>
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Volume</div>
                      <div className="font-medium truncate text-orange-400">
                        {enableRealtimeIntegration && realtimeData?.has(token.mint) ? (
                          <>
                            {formatVolume(realtimeData.get(token.mint)!.volume)}
                            <span className="text-xs ml-1 animate-pulse">🔴</span>
                          </>
                        ) : token.volume ? formatVolume(token.volume) : '$0'}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Holders</div>
                      <div className="font-medium truncate text-blue-400">
                        {enableRealtimeIntegration && realtimeData?.has(token.mint) ? (
                          <>
                            {formatHolders(realtimeData.get(token.mint)!.trader_count)}
                            <span className="text-xs ml-1 animate-pulse">🔴</span>
                          </>
                        ) : token.numHolders ? formatHolders(token.numHolders) : 'N/A'}
                      </div>
                    </div>
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Age</div>
                      <div className="font-medium truncate">{getTimeAgo(token.created_timestamp)}</div>
                    </div>
                  </div>

                  {/* Bonding Curve Progress Bar (only for non-graduated tokens) */}
                  {!token.complete && (
                    <div className="mt-auto pt-4 border-t border-[#222]">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs text-[#666]">Bonding Progress</span>
                        <span className="text-xs text-[#22c55e] font-medium">
                          {formatBondingCurveProgress(calculateBondingCurveProgress(token))}
                        </span>
                      </div>
                      <div className="w-full bg-[#222] rounded-full h-2 overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-[#22c55e] to-emerald-400 rounded-full transition-all duration-300"
                          style={{ width: `${Math.max(calculateBondingCurveProgress(token), 2)}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* Graduated tokens show different info */}
                  {token.complete && (
                    <div className="flex items-center gap-2 mt-auto pt-4 border-t border-[#222]">
                      <div className="flex items-center gap-1.5 text-xs text-[#666]">
                        <Clock size={12} />
                        <span>{getTimeAgo(token.last_trade_timestamp)}</span>
                      </div>
                      <div className="flex items-center gap-1.5 text-xs text-[#666] ml-auto">
                        <Users size={12} />
                        <span>{token.reply_count}</span>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-2 mt-3 pt-3 border-t border-[#222] justify-end min-h-[32px]">
                    <div className="flex gap-2">
                      {token.twitter && (
                        <a
                          href={`https://${token.twitter}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#666] hover:text-[#1DA1F2] transition-all duration-200 p-1.5 hover:bg-[#1DA1F2]/5 rounded-lg hover:scale-110 hover:shadow-lg"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Twitter size={14} />
                        </a>
                      )}
                      {token.website && (
                        <a
                          href={token.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#666] hover:text-[#22c55e] transition-all duration-200 p-1.5 hover:bg-[#22c55e]/5 rounded-lg hover:scale-110 hover:shadow-lg"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Globe size={14} />
                        </a>
                      )}
                      {token.telegram && (
                        <a
                          href={`https://t.me/${token.telegram}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#666] hover:text-[#229ED9] transition-all duration-200 p-1.5 hover:bg-[#229ED9]/5 rounded-lg hover:scale-110 hover:shadow-lg"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MessageCircle size={14} />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}