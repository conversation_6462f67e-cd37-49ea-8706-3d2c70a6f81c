import fetch from 'node-fetch';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

class TokenService {
  constructor() {
    this.apiKey = process.env.MORALIS_API_KEY;
    this.baseUrl = process.env.MORALIS_BASE_URL || 'https://solana-gateway.moralis.io';
    this.pumpfunDirectApiUrl = 'https://frontend-api-v3.pump.fun';
    this.defaultLimit = parseInt(process.env.DEFAULT_LIMIT) || 50;

    if (!this.apiKey) {
      throw new Error('MORALIS_API_KEY is required. Please check your .env file.');
    }
  }

  /**
   * Get new tokens from PumpFun exchange
   * @param {number} limit - Number of tokens to fetch (default: 50)
   * @returns {Promise<Object>} API response with new tokens
   */
  async getNewTokens(limit = this.defaultLimit) {
    try {
      const url = `${this.baseUrl}/token/mainnet/exchange/pumpfun/new?limit=${limit}`;

      const options = {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'X-API-Key': this.apiKey
        }
      };

      console.log(`Fetching ${limit} new tokens from PumpFun...`);
      console.log(`URL: ${url}`);

      const response = await fetch(url, options);

      // Log response headers
      console.log('\n📋 Response Headers:');
      for (const [key, value] of response.headers.entries()) {
        console.log(`  ${key}: ${value}`);
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();

      // Log the full response structure for debugging
      console.log('\n🔍 Full API Response Structure:');
      console.log('Response keys:', Object.keys(data));
      console.log('Response type:', typeof data);

      if (data.result && Array.isArray(data.result)) {
        console.log(`\n📊 Token array length: ${data.result.length}`);
        if (data.result.length > 0) {
          console.log('\n🔍 First token structure:');
          console.log('Token keys:', Object.keys(data.result[0]));
          console.log('Full first token:', JSON.stringify(data.result[0], null, 2));
        }
      }

      console.log(`\n✅ Successfully fetched ${data?.result?.length || 0} tokens`);

      // Save full response to file for analysis
      this.saveResponseToFile(data, 'latest_tokens_response.json');

      return data;

    } catch (error) {
      console.error('Error fetching new tokens:', error.message);
      throw error;
    }
  }

  /**
   * Get token details by address
   * @param {string} tokenAddress - The token contract address
   * @returns {Promise<Object>} Token details
   */
  async getTokenDetails(tokenAddress) {
    try {
      const url = `${this.baseUrl}/token/mainnet/${tokenAddress}`;
      
      const options = {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'X-API-Key': this.apiKey
        }
      };

      console.log(`Fetching details for token: ${tokenAddress}`);
      
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
      }
      
      const data = await response.json();
      
      return data;
      
    } catch (error) {
      console.error(`Error fetching token details for ${tokenAddress}:`, error.message);
      throw error;
    }
  }

  /**
   * Format enriched token data for display
   * @param {Array} tokens - Array of enriched token objects
   * @returns {Array} Formatted token data with all available fields
   */
  formatTokenData(tokens) {
    if (!tokens || !Array.isArray(tokens)) {
      return [];
    }

    return tokens.map((token, index) => {
      // Log all available fields for the first few tokens
      if (index < 2) {
        console.log(`\n🔍 Token ${index + 1} - All available fields (${token._enriched ? 'ENRICHED' : 'PUMPFUN ONLY'}):`);
        const fieldsToShow = Object.keys(token).filter(key => !key.startsWith('_'));
        console.log(`Field count: ${fieldsToShow.length}`);
        console.log(`Fields: ${fieldsToShow.join(', ')}`);
      }

      // Return all fields from the token object, with enhanced formatting for enriched data
      return {
        // Preserve all original fields
        ...token,

        // Add comprehensive formatted versions of all key fields
        _formatted: {
          // === CORE IDENTITY ===
          address: token.tokenAddress || 'N/A',
          name: token.name || 'Unknown',
          symbol: token.symbol || 'N/A',
          decimals: token.decimals || 'N/A',
          logo: token.logo || 'N/A',

          // === PRICING ===
          priceUsd: token.priceUsd || 'N/A',
          priceNative: token.priceNative || 'N/A',

          // === MARKET DATA ===
          marketCap: token.marketCap || token.fullyDilutedValuation || 'N/A',
          fdv: token.fdv || token.fullyDilutedValuation || 'N/A',
          liquidity: token.liquidity || 'N/A',

          // === TIMESTAMPS ===
          createdAt: token.createdAt || 'N/A',
          graduatedAt: token.graduatedAt || 'N/A',
          pairCreatedAt: token.pairCreatedAt ? new Date(token.pairCreatedAt).toISOString() : 'N/A',

          // === TRADING ACTIVITY ===
          volume24h: token.volume_h24 || 'N/A',
          volume1h: token.volume_h1 || 'N/A',
          totalTxns24h: token.total_txns_24h || 'N/A',
          buys24h: token.txns_h24_buys || 'N/A',
          sells24h: token.txns_h24_sells || 'N/A',
          buyRatio24h: token.buy_sell_ratio_24h || 'N/A',

          // === PRICE PERFORMANCE ===
          priceChange24h: token.priceChange_h24 || 'N/A',
          priceChange1h: token.priceChange_h1 || 'N/A',
          isTrendingUp: token.is_trending_up || false,

          // === PLATFORM INFO ===
          dexscreenerUrl: token.dexscreener_url || 'N/A',
          pairAddress: token.pairAddress || 'N/A',
          dexId: token.dexId || 'N/A',
          chainId: token.chainId || 'N/A',

          // === BONDING CURVE (PumpFun specific) ===
          bondingCurveProgress: token.bondingCurveProgress || 'N/A',

          // === ACTIVITY CLASSIFICATION ===
          activityLevel: token.activity_level || 'N/A',

          // === METADATA ===
          enriched: token._enriched || false,
          dataSources: token._data_sources || ['unknown'],
          fieldCount: token._field_count || 0,

          // === DERIVED FLAGS ===
          isBondingToken: token.bondingCurveProgress !== undefined,
          isGraduatedToken: token.graduatedAt !== undefined && token.graduatedAt !== null,
          hasVolume: (token.volume_h24 || 0) > 0,
          hasRecentActivity: (token.total_txns_24h || 0) > 0,
          isHighActivity: token.activity_level === 'high',
          nearGraduation: token.bondingCurveProgress && token.bondingCurveProgress > 90
        }
      };
    });
  }

  /**
   * Start monitoring new tokens with periodic updates
   * @param {number} intervalMs - Update interval in milliseconds
   * @param {Function} callback - Callback function to handle new tokens
   */
  startMonitoring(intervalMs = 30000, callback) {
    console.log(`Starting token monitoring with ${intervalMs}ms interval...`);
    
    const monitor = async () => {
      try {
        const data = await this.getNewTokens();
        if (callback && typeof callback === 'function') {
          callback(data);
        }
      } catch (error) {
        console.error('Monitoring error:', error.message);
      }
    };

    // Initial fetch
    monitor();
    
    // Set up periodic updates
    const intervalId = setInterval(monitor, intervalMs);
    
    return intervalId;
  }

  /**
   * Stop monitoring
   * @param {number} intervalId - The interval ID returned by startMonitoring
   */
  stopMonitoring(intervalId) {
    if (intervalId) {
      clearInterval(intervalId);
      console.log('Token monitoring stopped.');
    }
  }

  /**
   * Save API response to JSON file for analysis
   * @param {Object} data - The API response data
   * @param {string} filename - The filename to save to
   */
  saveResponseToFile(data, filename) {
    try {
      const dataDir = 'data';
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const filepath = path.join(dataDir, filename);
      const timestamp = new Date().toISOString();

      const fileData = {
        timestamp,
        responseHeaders: data.headers || {},
        ...data
      };

      fs.writeFileSync(filepath, JSON.stringify(fileData, null, 2));
      console.log(`💾 Response saved to: ${filepath}`);
    } catch (error) {
      console.error('Error saving response to file:', error.message);
    }
  }

  /**
   * Get enriched token data from DexScreener with batching support
   * @param {Array} tokenAddresses - Array of token addresses
   * @returns {Promise<Array>} DexScreener data for tokens
   */
  async getDexScreenerData(tokenAddresses) {
    try {
      if (!tokenAddresses || tokenAddresses.length === 0) {
        return [];
      }

      console.log(`\n🔍 Enriching ${tokenAddresses.length} tokens with DexScreener data...`);

      // DexScreener API limit: 30 addresses per request
      const BATCH_SIZE = 30;
      const batches = [];

      // Split addresses into batches of 30
      for (let i = 0; i < tokenAddresses.length; i += BATCH_SIZE) {
        batches.push(tokenAddresses.slice(i, i + BATCH_SIZE));
      }

      console.log(`📦 Split into ${batches.length} batches (max 30 tokens each)`);

      let allDexScreenerData = [];

      // Process each batch
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        const addressString = batch.join(',');
        const url = `https://api.dexscreener.com/tokens/v1/solana/${addressString}`;

        console.log(`\n� Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} tokens)...`);
        console.log(`URL: ${url}`);

        try {
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': '*/*'
            }
          });

          // Log response headers for first batch only
          if (batchIndex === 0) {
            console.log('\n📋 DexScreener Response Headers:');
            for (const [key, value] of response.headers.entries()) {
              console.log(`  ${key}: ${value}`);
            }
          }

          if (!response.ok) {
            console.error(`❌ Batch ${batchIndex + 1} failed: HTTP ${response.status} - ${response.statusText}`);
            continue; // Skip this batch but continue with others
          }

          const batchData = await response.json();

          // Debug: Log the actual response structure
          console.log(`\n🔍 Batch ${batchIndex + 1} raw response type:`, typeof batchData);
          console.log(`🔍 Batch ${batchIndex + 1} raw response keys:`, Object.keys(batchData || {}));

          // Handle different response formats
          let batchResults = [];
          if (Array.isArray(batchData)) {
            batchResults = batchData;
          } else if (batchData && typeof batchData === 'object') {
            // If it's an object, convert to array
            batchResults = Object.values(batchData);
          }

          console.log(`✅ Batch ${batchIndex + 1}: DexScreener returned ${batchResults.length} tokens`);

          // Add batch data to overall results
          allDexScreenerData = allDexScreenerData.concat(batchResults);

          // Save each batch response for debugging
          this.saveResponseToFile(batchResults, `dexscreener_batch_${batchIndex + 1}.json`);

          // Add delay between batches to be respectful to the API
          if (batchIndex < batches.length - 1) {
            console.log(`⏳ Waiting 1 second before next batch...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

        } catch (batchError) {
          console.error(`❌ Error processing batch ${batchIndex + 1}:`, batchError.message);
          continue; // Continue with next batch
        }
      }

      console.log(`\n🎯 Total DexScreener results: ${allDexScreenerData.length} tokens from ${batches.length} batches`);

      // Save combined response
      this.saveResponseToFile({
        timestamp: new Date().toISOString(),
        total_batches: batches.length,
        total_results: allDexScreenerData.length,
        data: allDexScreenerData
      }, 'dexscreener_combined_response.json');

      return allDexScreenerData;

    } catch (error) {
      console.error('Error fetching DexScreener data:', error.message);
      return [];
    }
  }

  /**
   * Enrich PumpFun tokens with DexScreener data
   * @param {Array} pumpfunTokens - Tokens from PumpFun API
   * @returns {Promise<Array>} Enriched token data
   */
  async enrichTokensWithDexScreener(pumpfunTokens) {
    try {
      if (!pumpfunTokens || pumpfunTokens.length === 0) {
        return [];
      }

      // Extract token addresses
      const tokenAddresses = pumpfunTokens
        .map(token => token.tokenAddress)
        .filter(address => address);

      console.log(`\n🔄 Starting enrichment process for ${tokenAddresses.length} tokens...`);

      // Get DexScreener data
      const dexScreenerData = await this.getDexScreenerData(tokenAddresses);

      // Create a map for quick lookup
      const dexScreenerMap = new Map();
      dexScreenerData.forEach(dexToken => {
        if (dexToken.baseToken?.address) {
          dexScreenerMap.set(dexToken.baseToken.address, dexToken);
        }
      });

      // Comprehensive field mapping and merging
      const enrichedTokens = pumpfunTokens.map(pumpToken => {
        const dexData = dexScreenerMap.get(pumpToken.tokenAddress);

        if (dexData) {
          // COMPREHENSIVE MERGE: All fields from both APIs with intelligent mapping
          return {
            // === UNIFIED CORE FIELDS (with priority logic) ===
            // Token Identity (DexScreener preferred for accuracy)
            tokenAddress: dexData.baseToken?.address || pumpToken.tokenAddress,
            name: dexData.baseToken?.name || pumpToken.name,
            symbol: dexData.baseToken?.symbol || pumpToken.symbol,

            // Token Metadata (PumpFun only)
            logo: pumpToken.logo,
            decimals: pumpToken.decimals,

            // Pricing (DexScreener preferred for real-time accuracy)
            priceUsd: dexData.priceUsd || pumpToken.priceUsd,
            priceNative: dexData.priceNative || pumpToken.priceNative,

            // Market Data (DexScreener preferred, PumpFun fallback)
            marketCap: dexData.marketCap || pumpToken.fullyDilutedValuation,
            fdv: dexData.fdv || pumpToken.fullyDilutedValuation,
            liquidity: pumpToken.liquidity, // PumpFun only

            // Timestamps
            createdAt: pumpToken.createdAt, // PumpFun only
            graduatedAt: pumpToken.graduatedAt, // PumpFun graduated tokens only
            pairCreatedAt: dexData.pairCreatedAt, // DexScreener only

            // === PUMPFUN EXCLUSIVE FIELDS ===
            fullyDilutedValuation: pumpToken.fullyDilutedValuation,
            bondingCurveProgress: pumpToken.bondingCurveProgress,

            // === DEXSCREENER EXCLUSIVE FIELDS ===
            // Platform Info
            chainId: dexData.chainId || 'solana',
            dexId: dexData.dexId,
            pairAddress: dexData.pairAddress,
            dexscreener_url: dexData.url,

            // Quote Token Info
            quoteToken_address: dexData.quoteToken?.address,
            quoteToken_name: dexData.quoteToken?.name,
            quoteToken_symbol: dexData.quoteToken?.symbol,

            // Transaction Data (all timeframes)
            txns_m5_buys: dexData.txns?.m5?.buys || 0,
            txns_m5_sells: dexData.txns?.m5?.sells || 0,
            txns_h1_buys: dexData.txns?.h1?.buys || 0,
            txns_h1_sells: dexData.txns?.h1?.sells || 0,
            txns_h6_buys: dexData.txns?.h6?.buys || 0,
            txns_h6_sells: dexData.txns?.h6?.sells || 0,
            txns_h24_buys: dexData.txns?.h24?.buys || 0,
            txns_h24_sells: dexData.txns?.h24?.sells || 0,

            // Volume Data (all timeframes)
            volume_m5: dexData.volume?.m5 || 0,
            volume_h1: dexData.volume?.h1 || 0,
            volume_h6: dexData.volume?.h6 || 0,
            volume_h24: dexData.volume?.h24 || 0,

            // Price Change Data (all timeframes)
            priceChange_m5: dexData.priceChange?.m5 || 0,
            priceChange_h1: dexData.priceChange?.h1 || 0,
            priceChange_h6: dexData.priceChange?.h6 || 0,
            priceChange_h24: dexData.priceChange?.h24 || 0,

            // === CALCULATED FIELDS ===
            // Trading Activity Metrics
            total_buys_24h: (dexData.txns?.h24?.buys || 0),
            total_sells_24h: (dexData.txns?.h24?.sells || 0),
            total_txns_24h: (dexData.txns?.h24?.buys || 0) + (dexData.txns?.h24?.sells || 0),
            buy_sell_ratio_24h: (dexData.txns?.h24?.sells || 0) > 0 ?
              ((dexData.txns?.h24?.buys || 0) / (dexData.txns?.h24?.sells || 0)).toFixed(2) : 'N/A',

            // Price Performance
            price_performance_24h: dexData.priceChange?.h24 || 0,
            is_trending_up: (dexData.priceChange?.h24 || 0) > 0,

            // Market Activity Level
            activity_level: this.calculateActivityLevel(dexData),

            // === METADATA ===
            _enriched: true,
            _enrichment_timestamp: new Date().toISOString(),
            _data_sources: ['pumpfun', 'dexscreener'],
            _field_count: Object.keys({...pumpToken, ...dexData}).length,

            // === RAW DATA PRESERVATION ===
            _pumpfun_original: pumpToken,
            _dexscreener_original: dexData
          };
        } else {
          // No DexScreener data - preserve all PumpFun data with additional metadata
          return {
            // All original PumpFun fields
            ...pumpToken,

            // Standardized null values for missing DexScreener fields
            chainId: 'solana',
            dexId: null,
            pairAddress: null,
            dexscreener_url: null,
            quoteToken_address: null,
            quoteToken_name: null,
            quoteToken_symbol: null,
            graduatedAt: pumpToken.graduatedAt || null, // Preserve graduation timestamp

            // Zero values for missing metrics
            txns_m5_buys: 0, txns_m5_sells: 0,
            txns_h1_buys: 0, txns_h1_sells: 0,
            txns_h6_buys: 0, txns_h6_sells: 0,
            txns_h24_buys: 0, txns_h24_sells: 0,
            volume_m5: 0, volume_h1: 0, volume_h6: 0, volume_h24: 0,
            priceChange_m5: 0, priceChange_h1: 0, priceChange_h6: 0, priceChange_h24: 0,

            // Calculated fields
            total_buys_24h: 0,
            total_sells_24h: 0,
            total_txns_24h: 0,
            buy_sell_ratio_24h: 'N/A',
            price_performance_24h: 0,
            is_trending_up: false,
            activity_level: 'pre-market',

            // Metadata
            _enriched: false,
            _enrichment_timestamp: new Date().toISOString(),
            _data_sources: ['pumpfun'],
            _field_count: Object.keys(pumpToken).length,
            _pumpfun_original: pumpToken
          };
        }
      });

      const enrichedCount = enrichedTokens.filter(token => token._enriched).length;
      const notEnrichedCount = enrichedTokens.filter(token => !token._enriched).length;

      console.log(`✅ Successfully enriched ${enrichedCount}/${pumpfunTokens.length} tokens with DexScreener data`);

      if (notEnrichedCount > 0) {
        console.log(`\n⚠️  ${notEnrichedCount} tokens not found in DexScreener:`);
        const notEnriched = enrichedTokens.filter(token => !token._enriched);
        notEnriched.slice(0, 5).forEach((token, index) => {
          const name = token.name || 'Unknown';
          const symbol = token.symbol || 'N/A';
          const progress = token.bondingCurveProgress ? `${token.bondingCurveProgress.toFixed(1)}%` : 'N/A';
          console.log(`   ${index + 1}. ${name} (${symbol}) - Progress: ${progress}`);
        });
        if (notEnrichedCount > 5) {
          console.log(`   ... and ${notEnrichedCount - 5} more`);
        }
        console.log(`\n💡 Note: Bonding tokens may not appear in DexScreener until they have sufficient trading activity or graduate from the bonding curve.`);
      }

      return enrichedTokens;

    } catch (error) {
      console.error('Error enriching tokens:', error.message);
      return pumpfunTokens; // Return original data if enrichment fails
    }
  }

  /**
   * Further enrich tokens with PumpFun Direct API data (third enrichment layer)
   * @param {Array} tokens - Array of tokens already enriched with DexScreener
   * @returns {Promise<Array>} Array of tokens with additional PumpFun Direct API data
   */
  async enrichTokensWithPumpfunDirectApi(tokens) {
    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      console.log('⚠️  No tokens provided for PumpFun Direct API enrichment');
      return [];
    }

    console.log(`\n🌐 Starting PumpFun Direct API enrichment for ${tokens.length} tokens...`);
    console.log(`📦 Using batches of 5 tokens with 1-second delays`);

    // Extract token addresses
    const tokenAddresses = tokens.map(token => token.tokenAddress).filter(addr => addr);

    if (tokenAddresses.length === 0) {
      console.log('⚠️  No valid token addresses found');
      return tokens;
    }

    // Get PumpFun Direct API data with batching (5 tokens per batch)
    const pumpfunDirectResults = await this.checkMultiplePumpfunDirectTokens(tokenAddresses, 5, 1000);

    // Merge PumpFun Direct API data with existing tokens
    const pumpfunDirectEnrichedTokens = tokens.map(token => {
      const pumpfunDirectResult = pumpfunDirectResults.find(result =>
        result.tokenAddress === token.tokenAddress
      );

      if (pumpfunDirectResult && pumpfunDirectResult.success && pumpfunDirectResult.data) {
        const pumpfunDirectData = pumpfunDirectResult.data;

        return {
          ...token,

          // Add PumpFun Direct API exclusive fields with pumpfun_ prefix
          pumpfun_description: pumpfunDirectData.description,
          pumpfun_creator: pumpfunDirectData.creator,
          pumpfun_complete: pumpfunDirectData.complete,
          pumpfun_bonding_curve: pumpfunDirectData.bonding_curve,
          pumpfun_associated_bonding_curve: pumpfunDirectData.associated_bonding_curve,
          pumpfun_raydium_pool: pumpfunDirectData.raydium_pool,
          pumpfun_virtual_sol_reserves: pumpfunDirectData.virtual_sol_reserves,
          pumpfun_virtual_token_reserves: pumpfunDirectData.virtual_token_reserves,
          pumpfun_website: pumpfunDirectData.website,
          pumpfun_twitter: pumpfunDirectData.twitter,
          pumpfun_telegram: pumpfunDirectData.telegram,
          pumpfun_metadata_uri: pumpfunDirectData.metadata_uri,
          pumpfun_nsfw: pumpfunDirectData.nsfw,
          pumpfun_is_currently_live: pumpfunDirectData.is_currently_live,
          pumpfun_created_timestamp: pumpfunDirectData.created_timestamp,

          // Update metadata
          _pumpfun_direct_enriched: true,
          _pumpfun_direct_enrichment_timestamp: new Date().toISOString(),
          _data_sources: [...(token._data_sources || []), 'pumpfun-direct-api'],
          _field_count: Object.keys(token).length + 15, // Approximate new field count
          _pumpfun_direct_original: pumpfunDirectData
        };
      } else {
        // PumpFun Direct API data not available
        return {
          ...token,
          _pumpfun_direct_enriched: false,
          _pumpfun_direct_enrichment_timestamp: new Date().toISOString(),
          _pumpfun_direct_error: pumpfunDirectResult ? pumpfunDirectResult.error : 'No result found'
        };
      }
    });

    const pumpfunDirectEnrichedCount = pumpfunDirectEnrichedTokens.filter(token => token._pumpfun_direct_enriched).length;
    const pumpfunDirectEnrichmentRate = ((pumpfunDirectEnrichedCount / tokens.length) * 100).toFixed(1);

    console.log(`\n✅ PumpFun Direct API enrichment complete:`);
    console.log(`   Total tokens: ${tokens.length}`);
    console.log(`   PumpFun Direct enriched: ${pumpfunDirectEnrichedCount}`);
    console.log(`   PumpFun Direct enrichment rate: ${pumpfunDirectEnrichmentRate}%`);

    return pumpfunDirectEnrichedTokens;
  }

  /**
   * Get bonding tokens from PumpFun exchange
   * @param {number} limit - Number of tokens to fetch (default: 50
   * @returns {Promise<Object>} API response with bonding tokens
   */
  async getBondingTokens(limit = 50) {
    try {
      const url = `${this.baseUrl}/token/mainnet/exchange/pumpfun/bonding?limit=${limit}`;

      const options = {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'X-API-Key': this.apiKey
        }
      };

      console.log(`Fetching ${limit} bonding tokens from PumpFun...`);
      console.log(`URL: ${url}`);

      const response = await fetch(url, options);

      // Log response headers
      console.log('\n📋 Bonding Tokens Response Headers:');
      for (const [key, value] of response.headers.entries()) {
        console.log(`  ${key}: ${value}`);
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();

      // Log the full response structure for debugging
      console.log('\n🔍 Full Bonding API Response Structure:');
      console.log('Response keys:', Object.keys(data));
      console.log('Response type:', typeof data);

      if (data.result && Array.isArray(data.result)) {
        console.log(`\n📊 Bonding token array length: ${data.result.length}`);
        if (data.result.length > 0) {
          console.log('\n🔍 First bonding token structure:');
          console.log('Token keys:', Object.keys(data.result[0]));
          console.log('Full first token:', JSON.stringify(data.result[0], null, 2));
        }
      }

      console.log(`\n✅ Successfully fetched ${data?.result?.length || 0} bonding tokens`);

      // Save full response to file for analysis
      this.saveResponseToFile(data, 'bonding_tokens_response.json');

      return data;

    } catch (error) {
      console.error('Error fetching bonding tokens:', error.message);
      throw error;
    }
  }

  /**
   * Get graduated tokens from PumpFun exchange
   * @param {number} limit - Number of tokens to fetch (default: 50)
   * @returns {Promise<Object>} API response with graduated tokens
   */
  async getGraduatedTokens(limit = 50) {
    try {
      const url = `${this.baseUrl}/token/mainnet/exchange/pumpfun/graduated?limit=${limit}`;

      const options = {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'X-API-Key': this.apiKey
        }
      };

      console.log(`Fetching ${limit} graduated tokens from PumpFun...`);
      console.log(`URL: ${url}`);

      const response = await fetch(url, options);

      // Log response headers
      console.log('\n📋 Graduated Tokens Response Headers:');
      for (const [key, value] of response.headers.entries()) {
        console.log(`  ${key}: ${value}`);
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();

      // Log the full response structure for debugging
      console.log('\n🔍 Full Graduated API Response Structure:');
      console.log('Response keys:', Object.keys(data));
      console.log('Response type:', typeof data);

      if (data.result && Array.isArray(data.result)) {
        console.log(`\n📊 Graduated token array length: ${data.result.length}`);
        if (data.result.length > 0) {
          console.log('\n🔍 First graduated token structure:');
          console.log('Token keys:', Object.keys(data.result[0]));
          console.log('Full first token:', JSON.stringify(data.result[0], null, 2));
        }
      }

      console.log(`\n✅ Successfully fetched ${data?.result?.length || 0} graduated tokens`);

      // Save full response to file for analysis
      this.saveResponseToFile(data, 'graduated_tokens_response.json');

      return data;

    } catch (error) {
      console.error('Error fetching graduated tokens:', error.message);
      throw error;
    }
  }

  /**
   * Get graduated tokens and enrich them with DexScreener data
   * @param {number} limit - Number of tokens to fetch (default: 50)
   * @returns {Promise<Array>} Enriched graduated token data
   */
  async getEnrichedGraduatedTokens(limit = 50) {
    try {
      console.log(`🚀 Fetching and enriching ${limit} graduated tokens...`);

      // Step 1: Get graduated tokens from PumpFun
      const pumpfunResponse = await this.getGraduatedTokens(limit);

      if (!pumpfunResponse?.result || !Array.isArray(pumpfunResponse.result)) {
        throw new Error('Invalid PumpFun graduated response format');
      }

      // Step 2: Enrich with DexScreener data
      const dexEnrichedTokens = await this.enrichTokensWithDexScreener(pumpfunResponse.result);

      // Step 3: Further enrich with PumpFun Direct API data
      const fullyEnrichedTokens = await this.enrichTokensWithPumpfunDirectApi(dexEnrichedTokens);

      // Step 4: Save enriched data
      this.saveResponseToFile({
        timestamp: new Date().toISOString(),
        type: 'graduated_tokens',
        pumpfun_count: pumpfunResponse.result.length,
        dex_enriched_count: dexEnrichedTokens.filter(t => t._enriched).length,
        pumpfun_direct_enriched_count: fullyEnrichedTokens.filter(t => t._pumpfun_direct_enriched).length,
        tokens: fullyEnrichedTokens
      }, 'enriched_graduated_tokens.json');

      return fullyEnrichedTokens;

    } catch (error) {
      console.error('Error in getEnrichedGraduatedTokens:', error.message);
      throw error;
    }
  }

  /**
   * Get bonding tokens and enrich them with DexScreener data
   * @param {number} limit - Number of tokens to fetch (default: 50)
   * @returns {Promise<Array>} Enriched bonding token data
   */
  async getEnrichedBondingTokens(limit = 50) {
    try {
      console.log(`🚀 Fetching and enriching ${limit} bonding tokens...`);

      // Step 1: Get bonding tokens from PumpFun
      const pumpfunResponse = await this.getBondingTokens(limit);

      if (!pumpfunResponse?.result || !Array.isArray(pumpfunResponse.result)) {
        throw new Error('Invalid PumpFun bonding response format');
      }

      // Step 2: Enrich with DexScreener data
      const dexEnrichedTokens = await this.enrichTokensWithDexScreener(pumpfunResponse.result);

      // Step 3: Further enrich with PumpFun Direct API data
      const fullyEnrichedTokens = await this.enrichTokensWithPumpfunDirectApi(dexEnrichedTokens);

      // Step 4: Save enriched data
      this.saveResponseToFile({
        timestamp: new Date().toISOString(),
        type: 'bonding_tokens',
        pumpfun_count: pumpfunResponse.result.length,
        dex_enriched_count: dexEnrichedTokens.filter(t => t._enriched).length,
        pumpfun_direct_enriched_count: fullyEnrichedTokens.filter(t => t._pumpfun_direct_enriched).length,
        tokens: fullyEnrichedTokens
      }, 'enriched_bonding_tokens.json');

      return fullyEnrichedTokens;

    } catch (error) {
      console.error('Error in getEnrichedBondingTokens:', error.message);
      throw error;
    }
  }

  /**
   * Get new tokens and enrich them with DexScreener data
   * @param {number} limit - Number of tokens to fetch (default: 50)
   * @returns {Promise<Array>} Enriched token data
   */
  async getEnrichedNewTokens(limit = this.defaultLimit) {
    try {
      console.log(`🚀 Fetching and enriching ${limit} new tokens...`);

      // Step 1: Get tokens from PumpFun
      const pumpfunResponse = await this.getNewTokens(limit);

      if (!pumpfunResponse?.result || !Array.isArray(pumpfunResponse.result)) {
        throw new Error('Invalid PumpFun response format');
      }

      // Step 2: Enrich with DexScreener data
      const dexEnrichedTokens = await this.enrichTokensWithDexScreener(pumpfunResponse.result);

      // Step 3: Further enrich with PumpFun Direct API data
      const fullyEnrichedTokens = await this.enrichTokensWithPumpfunDirectApi(dexEnrichedTokens);

      // Step 4: Save enriched data
      this.saveResponseToFile({
        timestamp: new Date().toISOString(),
        type: 'new_tokens',
        pumpfun_count: pumpfunResponse.result.length,
        dex_enriched_count: dexEnrichedTokens.filter(t => t._enriched).length,
        pumpfun_direct_enriched_count: fullyEnrichedTokens.filter(t => t._pumpfun_direct_enriched).length,
        tokens: fullyEnrichedTokens
      }, 'enriched_new_tokens.json');

      return fullyEnrichedTokens;

    } catch (error) {
      console.error('Error in getEnrichedNewTokens:', error.message);
      throw error;
    }
  }

  /**
   * Calculate activity level based on DexScreener data
   * @param {Object} dexData - DexScreener token data
   * @returns {string} Activity level classification
   */
  calculateActivityLevel(dexData) {
    if (!dexData || !dexData.txns || !dexData.volume) {
      return 'pre-market';
    }

    const txns24h = (dexData.txns.h24?.buys || 0) + (dexData.txns.h24?.sells || 0);
    const volume24h = dexData.volume.h24 || 0;

    if (txns24h >= 100 && volume24h >= 10000) {
      return 'high';
    } else if (txns24h >= 20 && volume24h >= 1000) {
      return 'medium';
    } else if (txns24h >= 5 && volume24h >= 100) {
      return 'low';
    } else {
      return 'minimal';
    }
  }

  /**
   * Get comprehensive field statistics
   * @param {Array} tokens - Array of enriched token objects
   * @returns {Object} Field statistics and analysis
   */
  getFieldStatistics(tokens) {
    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return {};
    }

    const allFields = new Set();
    const fieldSources = {};
    const fieldTypes = {};
    const fieldCoverage = {};

    tokens.forEach(token => {
      Object.keys(token).forEach(field => {
        allFields.add(field);

        // Track field coverage
        if (!fieldCoverage[field]) fieldCoverage[field] = 0;
        if (token[field] !== null && token[field] !== undefined && token[field] !== 'N/A') {
          fieldCoverage[field]++;
        }

        // Track field types
        if (!fieldTypes[field]) {
          fieldTypes[field] = typeof token[field];
        }

        // Track data sources
        if (field.startsWith('_')) return; // Skip metadata fields

        if (!fieldSources[field]) fieldSources[field] = new Set();

        if (token._data_sources) {
          token._data_sources.forEach(source => fieldSources[field].add(source));
        }
      });
    });

    // Convert sets to arrays for JSON serialization
    Object.keys(fieldSources).forEach(field => {
      fieldSources[field] = Array.from(fieldSources[field]);
    });

    return {
      totalFields: allFields.size,
      totalTokens: tokens.length,
      enrichedTokens: tokens.filter(t => t._enriched).length,
      fieldCoverage: Object.keys(fieldCoverage).map(field => ({
        field,
        coverage: fieldCoverage[field],
        percentage: ((fieldCoverage[field] / tokens.length) * 100).toFixed(1)
      })).sort((a, b) => b.coverage - a.coverage),
      fieldSources,
      fieldTypes,
      allFieldNames: Array.from(allFields).sort()
    };
  }

  /**
   * Get individual token data from PumpFun Direct API
   * @param {string} tokenAddress - The token contract address (mint)
   * @returns {Promise<Object|null>} Token data or null if not found
   */
  async getPumpfunDirectApiToken(tokenAddress) {
    try {
      const url = `${this.pumpfunDirectApiUrl}/coins/${tokenAddress}`;

      console.log(`🔍 Fetching token from PumpFun Direct API: ${tokenAddress}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'AP3X-PumP/1.0.0'
        },
        timeout: 10000
      });

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`   ❌ Token not found: ${tokenAddress}`);
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();

      console.log(`   ✅ Token data retrieved successfully`);

      return {
        tokenAddress,
        success: true,
        data,
        timestamp: new Date().toISOString(),
        source: 'pumpfun-direct-api'
      };

    } catch (error) {
      console.error(`   ❌ Error fetching token ${tokenAddress}:`, error.message);
      return {
        tokenAddress,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        source: 'pumpfun-direct-api'
      };
    }
  }

  /**
   * Check multiple tokens using PumpFun Direct API with rate limiting
   * @param {Array} tokenAddresses - Array of token addresses to check
   * @param {number} concurrency - Number of concurrent requests (default: 5)
   * @param {number} delayMs - Delay between batches in milliseconds (default: 1000)
   * @returns {Promise<Array>} Array of token results
   */
  async checkMultiplePumpfunDirectTokens(tokenAddresses, concurrency = 5, delayMs = 1000) {
    if (!tokenAddresses || !Array.isArray(tokenAddresses)) {
      throw new Error('Token addresses must be provided as an array');
    }

    console.log(`\n🚀 Checking ${tokenAddresses.length} tokens with PumpFun Direct API`);
    console.log(`⚙️  Concurrency: ${concurrency}, Delay: ${delayMs}ms`);
    console.log('=====================================');

    const results = [];
    const batches = [];

    // Split into batches for rate limiting
    for (let i = 0; i < tokenAddresses.length; i += concurrency) {
      batches.push(tokenAddresses.slice(i, i + concurrency));
    }

    console.log(`📦 Processing ${batches.length} batches...`);

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];

      console.log(`\n🔄 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} tokens)...`);

      // Process batch concurrently
      const batchPromises = batch.map(address => this.getPumpfunDirectApiToken(address));
      const batchResults = await Promise.all(batchPromises);

      results.push(...batchResults);

      const successCount = batchResults.filter(r => r.success).length;
      const foundCount = batchResults.filter(r => r.success && r.data !== null).length;

      console.log(`   ✅ Batch ${batchIndex + 1} complete: ${successCount}/${batch.length} successful, ${foundCount} tokens found`);

      // Add delay between batches (except for the last batch)
      if (batchIndex < batches.length - 1) {
        console.log(`   ⏳ Waiting ${delayMs}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    const totalSuccess = results.filter(r => r.success).length;
    const totalFound = results.filter(r => r.success && r.data !== null).length;
    const totalFailed = results.filter(r => !r.success).length;

    console.log(`\n📊 PUMPFUN DIRECT API CHECK COMPLETE:`);
    console.log(`=====================================`);
    console.log(`Total tokens checked: ${results.length}`);
    console.log(`Successful: ${totalSuccess}`);
    console.log(`Found: ${totalFound}`);
    console.log(`Failed: ${totalFailed}`);
    console.log(`Success rate: ${((totalSuccess / results.length) * 100).toFixed(1)}%`);
    console.log(`Found rate: ${((totalFound / results.length) * 100).toFixed(1)}%`);

    // Save results to file
    this.saveResponseToFile({
      timestamp: new Date().toISOString(),
      total_checked: results.length,
      successful: totalSuccess,
      found: totalFound,
      failed: totalFailed,
      success_rate: `${((totalSuccess / results.length) * 100).toFixed(1)}%`,
      found_rate: `${((totalFound / results.length) * 100).toFixed(1)}%`,
      results
    }, 'pumpfun_direct_api_check_results.json');

    return results;
  }

  /**
   * Check 50 tokens from existing enriched data using Frontend API v3
   * @param {string} sourceType - Type of tokens to check ('new', 'bonding', 'graduated', 'combined')
   * @returns {Promise<Array>} Array of Frontend API results
   */
  async check50TokensWithFrontendApi(sourceType = 'combined') {
    try {
      console.log(`📂 Loading ${sourceType} tokens for Frontend API check...`);

      let tokenAddresses = [];
      const dataFiles = {
        'new': 'data/enriched_new_tokens.json',
        'bonding': 'data/enriched_bonding_tokens.json',
        'graduated': 'data/enriched_graduated_tokens.json',
        'combined': ['data/enriched_new_tokens.json', 'data/enriched_bonding_tokens.json', 'data/enriched_graduated_tokens.json']
      };

      const filesToCheck = Array.isArray(dataFiles[sourceType]) ? dataFiles[sourceType] : [dataFiles[sourceType]];

      for (const filePath of filesToCheck) {
        try {
          if (fs.existsSync(filePath)) {
            const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

            let tokens = [];
            if (data.tokens && Array.isArray(data.tokens)) {
              tokens = data.tokens;
            } else if (Array.isArray(data)) {
              tokens = data;
            }

            const addresses = tokens
              .map(token => token.tokenAddress)
              .filter(address => address)
              .slice(0, sourceType === 'combined' ? 17 : 50); // Distribute evenly for combined

            tokenAddresses.push(...addresses);
            console.log(`   Loaded ${addresses.length} tokens from ${filePath}`);
          }
        } catch (error) {
          console.log(`   ⚠️  Could not load from ${filePath}:`, error.message);
        }
      }

      // Remove duplicates and limit to 50
      tokenAddresses = [...new Set(tokenAddresses)].slice(0, 50);

      if (tokenAddresses.length === 0) {
        // Fallback to sample tokens
        console.log('⚠️  No tokens found in data files, using sample addresses...');
        tokenAddresses = [
          '84sqwqUFnyEypQF5Y17xnToNA2tsfWw7AcUSgxDkCDfX', // devn
          '8RrA8LpqK8GQFXN7J5SRvBFZg5C3v2emGnCLJKZWpump', // This Is A Rug
          'So11111111111111111111111111111111111111112', // Wrapped SOL
          'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
          'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263' // Bonk
        ];
      }

      console.log(`✅ Loaded ${tokenAddresses.length} unique token addresses`);

      return await this.checkMultipleFrontendTokens(tokenAddresses, 5, 1000);

    } catch (error) {
      console.error('❌ Error checking tokens with Frontend API:', error.message);
      throw error;
    }
  }

  /**
   * Analyze Frontend API response structure
   * @param {Array} results - Array of Frontend API results
   * @returns {Object} Analysis of response structure
   */
  analyzeFrontendApiStructure(results) {
    const successfulResults = results.filter(r => r.success && r.data);

    if (successfulResults.length === 0) {
      return { error: 'No successful results to analyze' };
    }

    // Analyze field structure
    const allFields = new Set();
    const fieldTypes = {};
    const sampleData = {};

    successfulResults.forEach(result => {
      if (result.data && typeof result.data === 'object') {
        Object.keys(result.data).forEach(field => {
          allFields.add(field);

          if (!fieldTypes[field]) {
            fieldTypes[field] = typeof result.data[field];
            sampleData[field] = result.data[field];
          }
        });
      }
    });

    const analysis = {
      total_successful_responses: successfulResults.length,
      unique_fields: allFields.size,
      field_structure: Object.keys(fieldTypes).sort(),
      field_types: fieldTypes,
      sample_data: sampleData,

      // Field comparison with our existing data structure
      frontend_exclusive_fields: [],
      common_fields: [],
      missing_in_frontend: []
    };

    // Define field mappings between our standard fields and Frontend API fields
    const fieldMappings = {
      'tokenAddress': 'mint',
      'name': 'name',
      'symbol': 'symbol',
      'decimals': null, // Not available in Frontend API
      'logo': 'image_uri',
      'priceUsd': 'usd_market_cap',
      'priceNative': null, // Not directly available
      'marketCap': 'usd_market_cap',
      'liquidity': null, // Not available in Frontend API
      'createdAt': 'created_timestamp',
      'graduatedAt': null, // Not available in Frontend API
      'bondingCurveProgress': null // Not available in Frontend API
    };

    const ourStandardFields = Object.keys(fieldMappings);
    const frontendFields = analysis.field_structure;

    // Find fields that have mappings
    analysis.common_fields = ourStandardFields.filter(ourField => {
      const mappedField = fieldMappings[ourField];
      return mappedField && frontendFields.includes(mappedField);
    });

    // Find fields that don't have mappings (missing in Frontend API)
    analysis.missing_in_frontend = ourStandardFields.filter(ourField => {
      const mappedField = fieldMappings[ourField];
      return !mappedField || !frontendFields.includes(mappedField);
    });

    // Find Frontend API exclusive fields (not mapped to our standard fields)
    const mappedFrontendFields = Object.values(fieldMappings).filter(field => field !== null);
    analysis.frontend_exclusive_fields = frontendFields.filter(field =>
      !mappedFrontendFields.includes(field)
    );

    // Add field mapping information
    analysis.field_mappings = fieldMappings;
    analysis.available_mappings = {};
    ourStandardFields.forEach(ourField => {
      const mappedField = fieldMappings[ourField];
      if (mappedField && frontendFields.includes(mappedField)) {
        analysis.available_mappings[ourField] = mappedField;
      }
    });

    return analysis;
  }

  /**
   * Convert Frontend API token data to our standard format
   * @param {Object} frontendToken - Token data from Frontend API v3
   * @returns {Object} Token data in our standard format
   */
  convertFrontendApiToStandard(frontendToken) {
    if (!frontendToken || typeof frontendToken !== 'object') {
      return null;
    }

    return {
      // Core Identity (mapped fields)
      tokenAddress: frontendToken.mint,
      name: frontendToken.name,
      symbol: frontendToken.symbol,
      logo: frontendToken.image_uri,

      // Pricing & Market Data (mapped fields)
      priceUsd: frontendToken.usd_market_cap ? (frontendToken.usd_market_cap / 1000000000).toFixed(8) : null, // Convert from market cap
      marketCap: frontendToken.usd_market_cap,

      // Timestamps (mapped fields)
      createdAt: frontendToken.created_timestamp ? new Date(frontendToken.created_timestamp).toISOString() : null,

      // Fields not available in Frontend API (set to null)
      decimals: null,
      priceNative: null,
      liquidity: null,
      graduatedAt: null,
      bondingCurveProgress: null,

      // Frontend API exclusive fields (preserved)
      description: frontendToken.description,
      creator: frontendToken.creator,
      complete: frontendToken.complete,
      bonding_curve: frontendToken.bonding_curve,
      associated_bonding_curve: frontendToken.associated_bonding_curve,
      raydium_pool: frontendToken.raydium_pool,
      virtual_sol_reserves: frontendToken.virtual_sol_reserves,
      virtual_token_reserves: frontendToken.virtual_token_reserves,
      website: frontendToken.website,
      twitter: frontendToken.twitter,
      telegram: frontendToken.telegram,
      metadata_uri: frontendToken.metadata_uri,
      nsfw: frontendToken.nsfw,
      is_currently_live: frontendToken.is_currently_live,

      // Metadata
      _source: 'frontend-api-v3',
      _converted: true,
      _conversion_timestamp: new Date().toISOString(),
      _original_data: frontendToken
    };
  }

  /**
   * Convert multiple Frontend API results to standard format
   * @param {Array} frontendResults - Array of Frontend API results
   * @returns {Array} Array of tokens in standard format
   */
  convertMultipleFrontendApiToStandard(frontendResults) {
    if (!frontendResults || !Array.isArray(frontendResults)) {
      return [];
    }

    return frontendResults
      .filter(result => result.success && result.data)
      .map(result => this.convertFrontendApiToStandard(result.data))
      .filter(token => token !== null);
  }

  /**
   * Get 50 tokens from Frontend API v3 and convert to standard format
   * @param {string} sourceType - Type of tokens to check ('new', 'bonding', 'graduated', 'combined')
   * @returns {Promise<Array>} Array of tokens in standard format
   */
  async get50StandardizedFrontendTokens(sourceType = 'combined') {
    try {
      console.log(`🌐 Getting 50 standardized tokens from Frontend API v3...`);

      // Get raw Frontend API results
      const frontendResults = await this.check50TokensWithFrontendApi(sourceType);

      // Convert to standard format
      const standardizedTokens = this.convertMultipleFrontendApiToStandard(frontendResults);

      console.log(`✅ Converted ${standardizedTokens.length} tokens to standard format`);

      // Save standardized results
      this.saveResponseToFile({
        timestamp: new Date().toISOString(),
        source_type: sourceType,
        total_frontend_results: frontendResults.length,
        successful_conversions: standardizedTokens.length,
        tokens: standardizedTokens
      }, 'standardized_frontend_tokens.json');

      return standardizedTokens;

    } catch (error) {
      console.error('❌ Error getting standardized Frontend API tokens:', error.message);
      throw error;
    }
  }

  /**
   * Get all available field names from the latest response
   * @param {Array} tokens - Array of token objects
   * @returns {Array} Unique field names across all tokens
   */
  getAllFieldNames(tokens) {
    if (!tokens || !Array.isArray(tokens)) {
      return [];
    }

    const allFields = new Set();
    tokens.forEach(token => {
      Object.keys(token).forEach(field => allFields.add(field));
    });

    return Array.from(allFields).sort();
  }
}

export default TokenService;
