import { useState, useEffect } from 'react';
import { Search, ArrowLeft, ExternalLink, Globe, Twitter, MessageCircle, Info } from 'lucide-react';
import clsx from 'clsx';
import { useMessages } from '../contexts/MessagesContext';
import { CompactTokenChart } from './CompactTokenChart';
import { registerWidgetContext, updateWidgetContext, formatDexScreenerContext } from '../lib/widgetContext';

interface TokenLink {
  type: string;
  label: string;
  url: string;
}

interface TokenProfile {
  url: string;
  chainId: string;
  symbol: string;
  tokenAddress: string;
  icon: string;
  header: string;
  description: string;
  links: TokenLink[];
}

// Map DexScreener chain IDs to GMGN chain format
function getGMGNChain(chainId: string): string {
  const chainMap: { [key: string]: string } = {
    'ethereum': 'eth',
    'solana': 'sol',
    'bsc': 'bsc',
    'base': 'base',
    'arbitrum': 'eth', // GMGN might use 'eth' for Arbitrum
    'polygon': 'eth', // GMGN might use 'eth' for Polygon
    'avalanche': 'eth', // GMGN might use 'eth' for Avalanche
  };

  return chainMap[chainId.toLowerCase()] || 'sol'; // Default to Solana
}

// Build DexScreener embed URL with proper parameters
function buildDexScreenerEmbedUrl(tokenAddress: string, chainId: string): string {
  // Map chain IDs to DexScreener chain names
  const chainMap: { [key: string]: string } = {
    'ethereum': 'ethereum',
    'solana': 'solana',
    'bsc': 'bsc',
    'base': 'base',
    'arbitrum': 'arbitrum',
    'polygon': 'polygon',
    'avalanche': 'avalanche',
  };

  const chain = chainMap[chainId.toLowerCase()] || 'solana';

  // Build the embed URL with all the proper parameters
  const embedParams = new URLSearchParams({
    'embed': '1',
    'loadChartSettings': '0',
    'tabs': '0',
    'info': '0',
    'chartLeftToolbar': '0',
    'chartDefaultOnMobile': '1',
    'chartTheme': 'dark',
    'theme': 'dark',
    'chartStyle': '1',
    'chartType': 'usd',
    'interval': '15',
    'hideBranding': '1',
    'hideFooter': '1',
    'minimal': '1',
    'trades': '0'
  });

  return `https://dexscreener.com/${chain}/${tokenAddress}?${embedParams.toString()}`;
}

export function DexScreenerWidget({ isActive }: { isActive: boolean }) {
  const { messages } = useMessages();
  const [profiles, setProfiles] = useState<TokenProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Generate unique widget ID for context tracking
  const [widgetId] = useState(() => `dexscreener_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`);
  const [threadId] = useState(() => {
    // Try to get thread ID from URL or context - for now use a default
    return window.location.hash.includes('thread_')
      ? window.location.hash.split('thread_')[1]?.split('/')[0] || 'default'
      : 'default';
  });
  const [selectedProfile, setSelectedProfile] = useState<TokenProfile | null>(null);
  const [chartType, setChartType] = useState<'dexscreener' | 'gmgn'>(() => {
    // Load saved preference from localStorage
    const saved = localStorage.getItem('dexscreener-chart-type');
    return (saved === 'gmgn' || saved === 'dexscreener') ? saved : 'dexscreener'; // Default to DexScreener for consistency
  });
  const [isChartLoading, setIsChartLoading] = useState(true);

  // Handle chart type change and save preference
  const handleChartTypeChange = (type: 'dexscreener' | 'gmgn') => {
    setChartType(type);
    localStorage.setItem('dexscreener-chart-type', type);
  };

  useEffect(() => {
    const fetchProfiles = async () => {
      if (!isActive) return;

      setError(null);
      setIsLoading(true);

      try {
        const response = await fetch('https://api.dexscreener.com/token-profiles/latest/v1');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setProfiles(Array.isArray(data) ? data : [data]);
      } catch (error) {
        console.error('Error fetching profiles:', error);
        setError('Failed to load token profiles. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfiles();
  }, [isActive]);

  // Register widget context when data loads
  useEffect(() => {
    if (isActive && profiles.length > 0) {
      const contextData = formatDexScreenerContext('marketcap');
      registerWidgetContext(widgetId, 'dexscreener', threadId, contextData)
        .then(() => {
          console.log('📡 DexScreener widget context registered:', {
            widgetId,
            threadId,
            profileCount: profiles.length
          });
        })
        .catch(err => console.warn('⚠️ Failed to register DexScreener widget context:', err));
    }
  }, [isActive, profiles, widgetId, threadId]);

  const filteredProfiles = profiles.filter(profile =>
    profile.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    profile.tokenAddress?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle token selection from remnants
  useEffect(() => {
    if (!isActive) return;
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.type === 'user' && lastMessage.content.includes('tokenAddress:')) {
      const tokenAddress = lastMessage.content.split('tokenAddress:')[1].trim();
      const profile = profiles.find(p => p.tokenAddress.toLowerCase() === tokenAddress.toLowerCase());
      if (profile) {
        setSelectedProfile(profile);
      }
    }
  }, [messages, profiles, isActive]);
  if (selectedProfile) {
    return (
      <div className="bg-[#111] rounded-2xl p-4 sm:p-6 lg:p-8 w-full max-w-[95%] sm:max-w-[90%] mx-auto overflow-hidden">
        <div className="flex items-center gap-3 mb-6">
          <button
            onClick={() => setSelectedProfile(null)}
            className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200 group hover:scale-105 hover:shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 group-hover:translate-x-[-2px] transition-transform" />
          </button>
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              Token Details
              <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">
                {selectedProfile.chainId}
              </span>
            </h2>
          </div>
        </div>

        <div className="space-y-6">
          {/* Header Image */}
          {selectedProfile.header && (
            <div className="relative w-full h-48 rounded-xl overflow-hidden">
              <img
                src={selectedProfile.header}
                alt="Token Header"
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Token Info */}
          <div className="bg-[#0A0A0A] rounded-xl p-4 sm:p-6 hover:ring-1 hover:ring-[#222] transition-all duration-200 group">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Token Info */}
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                <img
                  src={selectedProfile.icon}
                  alt="Token Icon"
                  className="w-20 h-20 sm:w-24 sm:h-24 rounded-lg object-cover ring-1 ring-[#181818] group-hover:ring-[#22c55e] transition-all duration-200 shadow-lg group-hover:shadow-[#22c55e]/10 group-hover:scale-105"
                />
                <div className="flex-1 space-y-4">
                  <div className="space-y-2 text-center sm:text-left">
                    <h3 className="text-xl sm:text-2xl font-semibold bg-gradient-to-r from-white to-gray-400 text-transparent bg-clip-text">
                      {selectedProfile.tokenAddress.slice(0, 8)}...{selectedProfile.tokenAddress.slice(-6)}
                    </h3>
                    <p className="text-sm text-[#666] uppercase font-medium">{selectedProfile.chainId}</p>
                  </div>

                  <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2">
                    {selectedProfile.links.map((link, index) => (
                      <a
                        key={index}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={clsx(
                          "flex items-center gap-2 transition-all duration-200 px-3 py-1.5 rounded-lg text-sm hover:scale-105 hover:shadow-lg",
                          "text-[#666] bg-[#111]",
                          {
                            'hover:text-[#1DA1F2] hover:bg-[#1DA1F2]/5': link.type === 'twitter',
                            'hover:text-[#22c55e] hover:bg-[#22c55e]/5': link.type === 'website',
                            'hover:text-[#229ED9] hover:bg-[#229ED9]/5': link.type === 'telegram'
                          }
                        )}
                      >
                        {link.type === 'twitter' && <Twitter size={16} />}
                        {link.type === 'website' && <Globe size={16} />}
                        {link.type === 'telegram' && <MessageCircle size={16} />}
                        <span>{link.label}</span>
                      </a>
                    ))}

                    {/* DexScreener Link */}
                    <a
                      href={selectedProfile.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 transition-all duration-200 px-3 py-1.5 rounded-lg text-sm hover:scale-105 hover:shadow-lg text-[#666] bg-[#111] hover:text-[#22c55e] hover:bg-[#22c55e]/5"
                    >
                      <ExternalLink size={16} />
                      <span>DexScreener</span>
                    </a>
                  </div>
                </div>
              </div>

              {/* Right Column - Description */}
              {selectedProfile.description && (
                <div className="space-y-3 min-w-0">
                  <h4 className="font-medium text-sm flex items-center gap-2">
                    <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                    Description
                  </h4>
                  <div className="max-h-32 overflow-y-auto">
                    <p className="text-sm text-[#888] leading-relaxed break-words overflow-wrap-anywhere">
                      {selectedProfile.description}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Chart Section */}
          <div className="space-y-4">
            {/* Chart Type Toggle */}
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-white flex items-center gap-2">
                <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                Token Chart
              </h3>
              <div className="flex items-center gap-2 bg-[#0A0A0A] rounded-lg p-1">
                <button
                  onClick={() => handleChartTypeChange('dexscreener')}
                  className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                    chartType === 'dexscreener'
                      ? 'bg-[#22c55e] text-black'
                      : 'text-[#666] hover:text-white hover:bg-[#222]'
                  }`}
                >
                  DexScreener
                </button>
                <button
                  onClick={() => handleChartTypeChange('gmgn')}
                  className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                    chartType === 'gmgn'
                      ? 'bg-[#22c55e] text-black'
                      : 'text-[#666] hover:text-white hover:bg-[#222]'
                  }`}
                >
                  GMGN
                </button>
              </div>
            </div>

            {/* Chart Display */}
            {chartType === 'dexscreener' ? (
              <div className="bg-[#0A0A0A] rounded-xl overflow-hidden border border-[#222] hover:border-[#333] transition-all duration-200">
                <div className="flex items-center justify-between p-4 border-b border-[#222]">
                  <div className="flex items-center gap-2">
                    <ExternalLink className="w-4 h-4 text-[#22c55e]" />
                    <div>
                      <h3 className="font-medium text-white text-sm">DexScreener Chart</h3>
                      <p className="text-xs text-[#666]">Live trading data</p>
                    </div>
                  </div>
                  <a
                    href={selectedProfile.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-1.5 text-[#666] hover:text-white hover:bg-[#222] rounded-lg transition-all duration-200"
                    title="Open Full Chart"
                  >
                    <ExternalLink className="w-3.5 h-3.5" />
                  </a>
                </div>

                {/* DexScreener Embed with proper styling */}
                <div className="relative bg-[#111]">
                  {isChartLoading && (
                    <div className="absolute inset-0 bg-[#111] bg-opacity-90 flex items-center justify-center z-10">
                      <div className="flex items-center gap-2 text-[#666]">
                        <div className="w-4 h-4 border border-[#22c55e] border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm">Loading chart...</span>
                      </div>
                    </div>
                  )}
                  <style jsx>{`
                    .dexscreener-embed {
                      position: relative;
                      width: 100%;
                      padding-bottom: 125%;
                      overflow: hidden;
                    }
                    @media(min-width: 1400px) {
                      .dexscreener-embed {
                        padding-bottom: 65%;
                      }
                    }
                    .dexscreener-embed iframe {
                      position: absolute;
                      width: 100%;
                      height: calc(100% + 40px);
                      top: 0;
                      left: 0;
                      border: 0;
                      /* Extend iframe height to push "Tracked by" banner below visible area */
                    }
                    /* Overlay to hide any remaining branding */
                    .dexscreener-embed::after {
                      content: '';
                      position: absolute;
                      bottom: 0;
                      left: 0;
                      right: 0;
                      height: 40px;
                      background: #111;
                      pointer-events: none;
                      z-index: 1;
                    }
                  `}</style>
                  <div className="dexscreener-embed">
                    <iframe
                      src={buildDexScreenerEmbedUrl(selectedProfile.tokenAddress, selectedProfile.chainId)}
                      title={`DexScreener Chart for ${selectedProfile.symbol}`}
                      loading="lazy"
                      sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
                      onLoad={() => setIsChartLoading(false)}
                      onError={() => {
                        console.error('DexScreener embed failed to load');
                        setIsChartLoading(false);
                      }}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <CompactTokenChart
                tokenAddress={selectedProfile.tokenAddress}
                tokenName={selectedProfile.symbol}
                tokenSymbol={selectedProfile.symbol}
                chain={selectedProfile.chainId}
                height="600px"
                title="Live Chart"
                subtitle="Real-time data from DexScreener"
              />
            )}
          </div>


        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[95%] sm:max-w-[90%] mx-auto">
      <div className="flex items-center gap-3 mb-6">
        <div className="relative">
          <img 
            src="https://dexscreener.com/favicon.ico" 
            alt="DexScreener" 
            className="w-8 h-8 rounded-lg"
          />
        </div>
        <h2 className="text-lg font-semibold">DexScreener Terminal</h2>
        <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">LIVE</span>
      </div>
      
      {/* Search */}
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-[#666] w-4 h-4" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-[#0A0A0A] rounded-xl py-3 pl-11 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-[#222] placeholder-[#666] transition-all duration-200"
            placeholder="Search by description or address..."
          />
        </div>
      </div>

      {/* Token Grid */}
      <div className="mt-6">
        {isLoading ? (
          <div className="text-center text-[#666] py-8">
            <div className="animate-pulse">Loading token profiles...</div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-8 flex items-center justify-center gap-2">
            <Info size={16} />
            <span>{error}</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {filteredProfiles.map((profile) => (
              <div
                key={profile.tokenAddress}
                className="bg-[#0A0A0A] rounded-xl overflow-hidden hover:ring-1 hover:ring-[#222] transition-all duration-200 cursor-pointer group hover:scale-[1.02]"
                onClick={() => setSelectedProfile(profile)}
              >
                {/* Banner Image */}
                <div className="relative w-full h-32 bg-[#111]">
                  <img
                    src={profile.header || profile.icon}
                    alt={profile.symbol}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Content */}
                <div className="p-4 relative">
                  {/* Profile Picture */}
                  <div className="absolute -top-8 left-4">
                    <img
                      src={profile.icon}
                      alt={profile.symbol}
                      className="w-16 h-16 rounded-full object-cover ring-4 ring-[#0A0A0A] bg-[#111]"
                      onError={(e) => {
                        e.currentTarget.src = 'https://dexscreener.com/favicon.ico';
                      }}
                    />
                  </div>
                  
                  <div className="px-4 pb-4 space-y-3 mt-8">
                    <div className="flex items-center justify-between gap-2">
                      <h3 className="font-medium truncate text-lg">{profile.tokenAddress.slice(0, 8)}...{profile.tokenAddress.slice(-6)}</h3>
                      <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full">
                        {profile.chainId}
                      </span>
                    </div>
                    <p className="text-sm text-[#666] line-clamp-3">{profile.description}</p>
                    
                    {profile.links && profile.links.length > 0 && (
                      <div className="flex items-center flex-wrap gap-2 pt-3 border-t border-[#222]">
                        {profile.links.map((link, index) => (
                          <a
                            key={index}
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={(e) => e.stopPropagation()}
                            className={clsx(
                              "text-[#666] transition-all duration-200 hover:scale-105 p-1.5 rounded-lg",
                              {
                                'hover:text-[#1DA1F2] hover:bg-[#1DA1F2]/5': link.type === 'twitter',
                                'hover:text-[#22c55e] hover:bg-[#22c55e]/5': link.type === 'website',
                                'hover:text-[#229ED9] hover:bg-[#229ED9]/5': link.type === 'telegram'
                              }
                            )}
                          >
                            {link.type === 'twitter' && <Twitter size={14} />}
                            {link.type === 'website' && <Globe size={14} />}
                            {link.type === 'telegram' && <MessageCircle size={14} />}
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}