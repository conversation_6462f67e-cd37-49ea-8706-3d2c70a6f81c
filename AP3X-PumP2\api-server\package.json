{"name": "ap3x-pump-api-server", "version": "1.0.0", "description": "REST API server for AP3X-PumP Token Intelligence Platform", "main": "server.js", "type": "module", "scripts": {"start": "node simple-server.js", "dev": "node --watch simple-server.js", "test": "node test-simple.js", "full": "node server.js", "test-full": "node test-endpoints.js"}, "keywords": ["pumpfun", "solana", "tokens", "api", "defi", "crypto"], "author": "AP3X-PumP Team", "license": "MIT", "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "node-fetch": "^3.3.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.0.2"}}