/**
 * Test script to verify Supabase connection and data fetching
 */

const projectUrl = 'https://tmbhwnnmqbvavtfleidy.supabase.co';
const apiKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRtYmh3bm5tcWJ2YXZ0ZmxlaWR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ3MjEyMDAsImV4cCI6MjA3MDI5NzIwMH0.jz48XryhPkg0hZQfvDdwe-ls3WED6eyqiDpZTvykFMM';

async function testSupabaseConnection() {
  console.log('🧪 Testing Supabase connection...');
  
  try {
    // Test connection
    const response = await fetch(`${projectUrl}/rest/v1/new_tokens?select=count&limit=1`, {
      headers: {
        'apikey': apiKey,
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log('✅ Supabase connection successful!');
    
    // Test each table
    const tables = ['new_tokens', 'bonding_tokens', 'graduated_tokens'];
    
    for (const table of tables) {
      console.log(`\n📊 Testing ${table} table...`);
      
      // First check if table exists and has any records
      const countResponse = await fetch(`${projectUrl}/rest/v1/${table}?select=count`, {
        headers: {
          'apikey': apiKey,
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'Prefer': 'count=exact'
        }
      });

      if (countResponse.ok) {
        const countText = countResponse.headers.get('content-range');
        console.log(`   Total records in ${table}: ${countText}`);
      }

      const tableResponse = await fetch(`${projectUrl}/rest/v1/${table}?select=token_address,data,inserted_at&order=inserted_at.desc&limit=3`, {
        headers: {
          'apikey': apiKey,
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!tableResponse.ok) {
        console.error(`❌ Error fetching ${table}: ${tableResponse.status} ${tableResponse.statusText}`);
        continue;
      }

      const data = await tableResponse.json();
      console.log(`✅ ${table}: Found ${data.length} records`);
      
      if (data.length > 0) {
        const sample = data[0];
        console.log(`   Sample token: ${sample.data?.name || 'Unknown'} (${sample.data?.symbol || 'N/A'})`);
        console.log(`   Address: ${sample.token_address}`);
        console.log(`   Market Cap: $${sample.data?.marketCap || sample.data?.usd_market_cap || 'N/A'}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Supabase test failed:', error.message);
  }
}

// Run the test
testSupabaseConnection();
