import fs from 'fs';
import path from 'path';

class CSVExporter {
  constructor() {
    this.outputDir = 'csv_exports';
    this.ensureOutputDir();
  }

  /**
   * Ensure the output directory exists
   */
  ensureOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Convert enriched tokens to CSV format
   * @param {Array} tokens - Array of enriched token objects
   * @param {string} tokenType - Type of tokens (new, bonding, graduated)
   * @returns {string} CSV file path
   */
  exportToCSV(tokens, tokenType = 'tokens') {
    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      throw new Error('No tokens provided for CSV export');
    }

    console.log(`\n📊 Exporting ${tokens.length} ${tokenType} tokens to CSV...`);

    // Define the core fields we want in the CSV (database-ready)
    const csvFields = [
      // Core Identity
      { key: 'tokenAddress', header: 'token_address' },
      { key: 'name', header: 'name' },
      { key: 'symbol', header: 'symbol' },
      { key: 'decimals', header: 'decimals' },
      { key: 'logo', header: 'logo_url' },
      
      // Pricing
      { key: 'priceUsd', header: 'price_usd' },
      { key: 'priceNative', header: 'price_native' },
      
      // Market Data
      { key: 'marketCap', header: 'market_cap' },
      { key: 'fdv', header: 'fully_diluted_valuation' },
      { key: 'liquidity', header: 'liquidity' },
      
      // Timestamps
      { key: 'createdAt', header: 'created_at' },
      { key: 'graduatedAt', header: 'graduated_at' },
      { key: 'pairCreatedAt', header: 'pair_created_at' },
      
      // Trading Activity
      { key: 'total_txns_24h', header: 'total_transactions_24h' },
      { key: 'total_buys_24h', header: 'total_buys_24h' },
      { key: 'total_sells_24h', header: 'total_sells_24h' },
      { key: 'buy_sell_ratio_24h', header: 'buy_sell_ratio_24h' },
      
      // Volume
      { key: 'volume_h24', header: 'volume_24h' },
      { key: 'volume_h6', header: 'volume_6h' },
      { key: 'volume_h1', header: 'volume_1h' },
      
      // Price Performance
      { key: 'priceChange_h24', header: 'price_change_24h' },
      { key: 'priceChange_h6', header: 'price_change_6h' },
      { key: 'priceChange_h1', header: 'price_change_1h' },
      { key: 'is_trending_up', header: 'is_trending_up' },
      
      // Platform Info
      { key: 'dexscreener_url', header: 'dexscreener_url' },
      { key: 'pairAddress', header: 'pair_address' },
      { key: 'dexId', header: 'dex_id' },
      { key: 'chainId', header: 'chain_id' },
      
      // PumpFun Specific
      { key: 'bondingCurveProgress', header: 'bonding_curve_progress' },
      { key: 'fullyDilutedValuation', header: 'pumpfun_fdv' },
      
      // Classification
      { key: 'activity_level', header: 'activity_level' },
      
      // Metadata
      { key: '_enriched', header: 'is_enriched' },
      { key: '_enrichment_timestamp', header: 'enrichment_timestamp' },
      { key: '_field_count', header: 'field_count' }
    ];

    // Create CSV header
    const headers = csvFields.map(field => field.header);
    let csvContent = headers.join(',') + '\n';

    // Process each token
    tokens.forEach((token, index) => {
      const row = csvFields.map(field => {
        let value = token[field.key];
        
        // Handle different data types and null values
        if (value === null || value === undefined || value === 'N/A') {
          return '';
        }
        
        // Handle arrays (convert to JSON string)
        if (Array.isArray(value)) {
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        }
        
        // Handle objects (convert to JSON string)
        if (typeof value === 'object') {
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        }
        
        // Handle strings (escape quotes and wrap in quotes if contains comma)
        if (typeof value === 'string') {
          value = value.replace(/"/g, '""'); // Escape quotes
          if (value.includes(',') || value.includes('\n') || value.includes('"')) {
            return `"${value}"`;
          }
          return value;
        }
        
        // Handle numbers and booleans
        return value.toString();
      });
      
      csvContent += row.join(',') + '\n';
      
      if ((index + 1) % 10 === 0) {
        console.log(`   Processed ${index + 1}/${tokens.length} tokens...`);
      }
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${tokenType}_tokens_${timestamp}.csv`;
    const filepath = path.join(this.outputDir, filename);

    // Write to file
    fs.writeFileSync(filepath, csvContent, 'utf8');

    console.log(`✅ CSV export complete: ${filepath}`);
    console.log(`📊 Exported ${tokens.length} tokens with ${headers.length} columns`);

    return filepath;
  }

  /**
   * Export tokens with statistics
   * @param {Array} tokens - Array of enriched token objects
   * @param {string} tokenType - Type of tokens
   * @returns {Object} Export results with statistics
   */
  exportWithStats(tokens, tokenType = 'tokens') {
    const filepath = this.exportToCSV(tokens, tokenType);
    
    // Calculate statistics
    const stats = this.calculateExportStats(tokens);
    
    // Save statistics to separate file
    const statsFilename = `${tokenType}_export_stats_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    const statsFilepath = path.join(this.outputDir, statsFilename);
    
    fs.writeFileSync(statsFilepath, JSON.stringify(stats, null, 2), 'utf8');
    
    console.log(`📈 Export statistics saved: ${statsFilepath}`);
    
    return {
      csvFile: filepath,
      statsFile: statsFilepath,
      stats
    };
  }

  /**
   * Calculate export statistics
   * @param {Array} tokens - Array of enriched token objects
   * @returns {Object} Statistics object
   */
  calculateExportStats(tokens) {
    const enrichedTokens = tokens.filter(t => t._enriched);
    const bondingTokens = tokens.filter(t => t.bondingCurveProgress !== undefined);
    const graduatedTokens = tokens.filter(t => t.graduatedAt !== undefined);
    
    // Activity level distribution
    const activityLevels = {};
    tokens.forEach(token => {
      const level = token.activity_level || 'unknown';
      activityLevels[level] = (activityLevels[level] || 0) + 1;
    });

    // Price range analysis
    const prices = tokens
      .map(t => parseFloat(t.priceUsd))
      .filter(p => !isNaN(p) && p > 0)
      .sort((a, b) => a - b);

    // Volume analysis
    const volumes = tokens
      .map(t => parseFloat(t.volume_h24))
      .filter(v => !isNaN(v) && v > 0)
      .sort((a, b) => a - b);

    return {
      export_timestamp: new Date().toISOString(),
      total_tokens: tokens.length,
      enriched_tokens: enrichedTokens.length,
      enrichment_rate: ((enrichedTokens.length / tokens.length) * 100).toFixed(1) + '%',
      
      token_types: {
        bonding_tokens: bondingTokens.length,
        graduated_tokens: graduatedTokens.length,
        new_tokens: tokens.length - bondingTokens.length - graduatedTokens.length
      },
      
      activity_distribution: activityLevels,
      
      price_analysis: prices.length > 0 ? {
        min_price: prices[0],
        max_price: prices[prices.length - 1],
        median_price: prices[Math.floor(prices.length / 2)],
        tokens_with_price: prices.length
      } : null,
      
      volume_analysis: volumes.length > 0 ? {
        min_volume: volumes[0],
        max_volume: volumes[volumes.length - 1],
        median_volume: volumes[Math.floor(volumes.length / 2)],
        tokens_with_volume: volumes.length
      } : null,
      
      data_quality: {
        tokens_with_names: tokens.filter(t => t.name && t.name !== 'Unknown').length,
        tokens_with_symbols: tokens.filter(t => t.symbol && t.symbol !== 'N/A').length,
        tokens_with_logos: tokens.filter(t => t.logo && t.logo !== 'N/A').length,
        tokens_with_volume: tokens.filter(t => t.volume_h24 && parseFloat(t.volume_h24) > 0).length
      }
    };
  }

  /**
   * Export all token types to separate CSV files
   * @param {Object} tokenData - Object containing arrays of different token types
   * @returns {Array} Array of export results
   */
  exportAllTypes(tokenData) {
    const results = [];
    
    if (tokenData.newTokens && tokenData.newTokens.length > 0) {
      results.push(this.exportWithStats(tokenData.newTokens, 'new'));
    }
    
    if (tokenData.bondingTokens && tokenData.bondingTokens.length > 0) {
      results.push(this.exportWithStats(tokenData.bondingTokens, 'bonding'));
    }
    
    if (tokenData.graduatedTokens && tokenData.graduatedTokens.length > 0) {
      results.push(this.exportWithStats(tokenData.graduatedTokens, 'graduated'));
    }
    
    return results;
  }
}

export default CSVExporter;
